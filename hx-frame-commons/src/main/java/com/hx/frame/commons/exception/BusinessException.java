package com.hx.frame.commons.exception;

import com.hx.frame.commons.response.ResultCode;
import lombok.Getter;

/**
 * 业务异常类
 * 用于封装业务逻辑处理过程中出现的异常情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-18
 */
@Getter
public class BusinessException extends BaseException {

    /**
     * 构造一个业务异常
     *
     * @param message 异常信息
     */
    public BusinessException(String message) {
        super(message, ResultCode.FAILED.getCode());
    }

    /**
     * 构造一个业务异常
     *
     * @param resultCode 结果码
     */
    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage(), resultCode.getCode());
    }

    /**
     * 构造一个业务异常
     *
     * @param resultCode 结果码
     * @param message    异常信息
     */
    public BusinessException(ResultCode resultCode, String message) {
        super(message, resultCode.getCode());
    }

    /**
     * 构造一个业务异常
     *
     * @param message 异常信息
     * @param cause   原始异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, ResultCode.FAILED.getCode(), cause);
    }
}