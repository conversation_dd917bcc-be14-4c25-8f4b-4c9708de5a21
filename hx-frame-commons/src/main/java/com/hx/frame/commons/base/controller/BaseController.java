package com.hx.frame.commons.base.controller;

import com.hx.frame.commons.base.entity.BaseEntity;
import com.hx.frame.commons.base.service.IBaseService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 基础控制器
 * 提供服务层注入
 *
 * @param <T> 实体类型，必须继承自BaseEntity
 * @param <S> 服务接口类型，必须继承自IBaseService
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
public abstract class BaseController<T extends BaseEntity, S extends IBaseService<T>> {

    /**
     * 注入的服务实例
     */
    @Autowired(required = false)
    protected S service;
}