package com.hx.frame.commons.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 统一API响应结果封装类
 * 用于封装所有接口的返回结果，包含状态码、提示信息和数据
 * 支持链式调用，可以通过setXxx方法进行字段设置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-18
 */
@Data
@Accessors(chain = true)
public class Result<T> {
    /**
     * 状态码
     */
    private Integer code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 成功返回结果
     *
     * @param data 获取的数据
     * @return 成功的响应结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<T>()
                .setCode(ResultCode.SUCCESS.getCode())
                .setMessage(ResultCode.SUCCESS.getMessage())
                .setData(data);
    }

    /**
     * 成功返回结果，不带数据
     *
     * @param <T> 泛型参数
     * @return 成功的响应结果
     */
    public static <T> Result<T> success() {
        return new Result<T>()
                .setCode(ResultCode.SUCCESS.getCode())
                .setMessage(ResultCode.SUCCESS.getMessage());
    }

    /**
     * 失败返回结果
     *
     * @param resultCode 错误码
     * @param <T>        泛型参数
     * @return 失败的响应结果
     */
    public static <T> Result<T> failed(ResultCode resultCode) {
        return new Result<T>()
                .setCode(resultCode.getCode())
                .setMessage(resultCode.getMessage());
    }

    /**
     * 失败返回结果
     *
     * @param resultCode 错误码
     * @param message    错误信息
     * @param <T>        泛型参数
     * @return 失败的响应结果
     */
    public static <T> Result<T> failed(ResultCode resultCode, String message) {
        return new Result<T>()
                .setCode(resultCode.getCode())
                .setMessage(message);
    }

    /**
     * 失败返回结果
     *
     * @param message 提示信息
     * @param <T>     泛型参数
     * @return 失败的响应结果
     */
    public static <T> Result<T> failed(String message) {
        return new Result<T>()
                .setCode(ResultCode.FAILED.getCode())
                .setMessage(message);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param <T> 泛型参数
     * @return 参数验证失败的响应结果
     */
    public static <T> Result<T> validateFailed() {
        return failed(ResultCode.VALIDATE_FAILED);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param message 提示信息
     * @param <T>     泛型参数
     * @return 参数验证失败的响应结果
     */
    public static <T> Result<T> validateFailed(String message) {
        return new Result<T>()
                .setCode(ResultCode.VALIDATE_FAILED.getCode())
                .setMessage(message);
    }

    /**
     * 未登录返回结果
     *
     * @param data 数据
     * @param <T>  泛型参数
     * @return 未登录的响应结果
     */
    public static <T> Result<T> unauthorized(T data) {
        return new Result<T>()
                .setCode(ResultCode.UNAUTHORIZED.getCode())
                .setMessage(ResultCode.UNAUTHORIZED.getMessage())
                .setData(data);
    }

    /**
     * 未授权返回结果
     *
     * @param data 数据
     * @param <T>  泛型参数
     * @return 未授权的响应结果
     */
    public static <T> Result<T> forbidden(T data) {
        return new Result<T>()
                .setCode(ResultCode.FORBIDDEN.getCode())
                .setMessage(ResultCode.FORBIDDEN.getMessage())
                .setData(data);
    }

    /**
     * 资源冲突返回结果
     *
     * @param message 提示信息
     * @param <T>     泛型参数
     * @return 资源冲突的响应结果
     */
    public static <T> Result<T> conflict(String message) {
        return new Result<T>()
                .setCode(ResultCode.CONFLICT.getCode())
                .setMessage(message);
    }
}