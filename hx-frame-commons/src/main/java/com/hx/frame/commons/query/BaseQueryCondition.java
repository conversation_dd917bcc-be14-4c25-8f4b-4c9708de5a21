package com.hx.frame.commons.query;

import lombok.Data;
import lombok.Getter;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * 基础查询条件类
 * 所有查询条件类的父类
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
@Data
public abstract class BaseQueryCondition<T> {

    /**
     * 查询条件列表
     */
    private final List<QueryCondition> conditions = new ArrayList<>();
    /**
     * 页码（从1开始）
     */
    private Integer pageNum = 1;
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection;

    /**
     * 构建查询条件
     * 将属性转换为查询条件
     */
    public abstract void buildConditions();

    /**
     * 添加等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     */
    protected void eq(String fieldName, Object value) {
        if (value != null && (!(value instanceof String) || StringUtils.hasText((String) value))) {
            conditions.add(new QueryCondition(fieldName, "EQ", value));
        }
    }

    /**
     * 添加模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value     值
     */
    protected void like(String fieldName, String value) {
        if (StringUtils.hasText(value)) {
            conditions.add(new QueryCondition(fieldName, "LIKE", value));
        }
    }

    /**
     * 添加大于等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     */
    protected void ge(String fieldName, Object value) {
        if (value != null) {
            conditions.add(new QueryCondition(fieldName, "GE", value));
        }
    }

    /**
     * 添加小于等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     */
    protected void le(String fieldName, Object value) {
        if (value != null) {
            conditions.add(new QueryCondition(fieldName, "LE", value));
        }
    }

    /**
     * 添加不等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     */
    protected void ne(String fieldName, Object value) {
        if (value != null && (!(value instanceof String) || StringUtils.hasText((String) value))) {
            conditions.add(new QueryCondition(fieldName, "NE", value));
        }
    }

    /**
     * 添加in条件（包含于列表）
     *
     * @param fieldName 字段名
     * @param values    值列表
     */
    protected void in(String fieldName, List<?> values) {
        if (values != null && !values.isEmpty()) {
            conditions.add(new QueryCondition(fieldName, "IN", values));
        }
    }

    /**
     * 添加区间查询条件
     *
     * @param fieldName 字段名
     * @param start     区间开始值
     * @param end       区间结束值
     */
    protected void between(String fieldName, Comparable<?> start, Comparable<?> end) {
        if (start != null && end != null) {
            conditions.add(new QueryCondition(fieldName, "BETWEEN", new Object[]{start, end}));
        } else if (start != null) {
            ge(fieldName, start);
        } else if (end != null) {
            le(fieldName, end);
        }
    }

    /**
     * 转换为分页对象
     * 注意：将前端传入的页码（从1开始）转换为JPA页码（从0开始）
     *
     * @return 分页对象
     */
    public Pageable toPageable() {
        // 页码从1开始转换为从0开始
        int jpaPageNum = pageNum > 0 ? pageNum - 1 : 0;

        // 创建排序对象
        Sort sort = Sort.unsorted();
        if (StringUtils.hasText(sortField)) {
            sort = Sort.by(Sort.Direction.fromString(sortDirection), sortField);
        }

        // 创建分页对象
        return PageRequest.of(jpaPageNum, pageSize, sort);
    }

    /**
     * 转换为查询规范
     *
     * @return 查询规范
     */
    @SuppressWarnings("unchecked")
    public Specification<T> toSpecification() {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            for (QueryCondition condition : conditions) {
                String operator = condition.getOperator();
                String fieldName = condition.getFieldName();
                Object value = condition.getValue();

                switch (operator) {
                    case "EQ":
                        predicates.add(cb.equal(root.get(fieldName), value));
                        break;
                    case "LIKE":
                        predicates.add(cb.like(root.get(fieldName), "%" + value + "%"));
                        break;
                    case "GE":
                        predicates.add(cb.greaterThanOrEqualTo(root.get(fieldName), (Comparable) value));
                        break;
                    case "LE":
                        predicates.add(cb.lessThanOrEqualTo(root.get(fieldName), (Comparable) value));
                        break;
                    case "NE":
                        predicates.add(cb.notEqual(root.get(fieldName), value));
                        break;
                    case "IN":
                        predicates.add(root.get(fieldName).in((List<?>) value));
                        break;
                    case "BETWEEN":
                        Object[] range = (Object[]) value;
                        predicates.add(cb.between(root.get(fieldName), (Comparable) range[0], (Comparable) range[1]));
                        break;
                    // 可以根据需要添加其他操作符的处理
                }
            }

            return predicates.isEmpty() ? null : cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 查询条件内部类
     */
    @Getter
    protected static class QueryCondition {
        private final String fieldName;
        private final String operator;
        private final Object value;

        public QueryCondition(String fieldName, String operator, Object value) {
            this.fieldName = fieldName;
            this.operator = operator;
            this.value = value;
        }

    }
}