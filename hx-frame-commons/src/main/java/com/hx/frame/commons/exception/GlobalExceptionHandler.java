package com.hx.frame.commons.exception;

import com.hx.frame.commons.response.Result;
import com.hx.frame.commons.response.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.StaleObjectStateException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * 统一处理系统中的各类异常，包括业务异常、参数校验异常等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-18
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理基础异常
     *
     * @param e 基础异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = BaseException.class)
    public Result<Void> handleBaseException(BaseException e) {
        log.error("基础异常：{}", e.getMessage(), e);
        // 使用默认的ResultCode.FAILED，并设置自定义消息
        return Result.failed(ResultCode.FAILED, e.getMessage());
    }

    /**
     * 处理自定义业务异常
     *
     * @param e 业务异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.error("业务异常：{}", e.getMessage(), e);
        return Result.failed(e.getMessage());
    }

    /**
     * 处理认证异常
     *
     * @param e 认证异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = AuthenticationException.class)
    public Result<Void> handleAuthenticationException(AuthenticationException e) {
        log.error("认证异常：{}", e.getMessage(), e);
        // 使用Result.failed方法返回Result<Void>类型
        return Result.failed(ResultCode.UNAUTHORIZED, e.getMessage());
    }

    /**
     * 处理权限不足异常
     *
     * @param e 权限不足异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = ForbiddenException.class)
    public Result<Void> handleForbiddenException(ForbiddenException e) {
        log.error("权限不足异常：{}", e.getMessage(), e);
        // 使用Result.failed方法返回Result<Void>类型
        return Result.failed(ResultCode.FORBIDDEN, e.getMessage());
    }

    /**
     * 处理乐观锁异常
     *
     * @param e 乐观锁异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = {ObjectOptimisticLockingFailureException.class, StaleObjectStateException.class})
    public Result<Void> handleOptimisticLockingFailureException(Exception e) {
        log.error("乐观锁异常：{}", e.getMessage(), e);
        return Result.failed(ResultCode.CONFLICT, "记录已被更改，请刷新页面后再尝试");
    }

    /**
     * 处理参数校验异常
     *
     * @param e 参数校验异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result<Void> handleValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getField() + fieldError.getDefaultMessage();
            }
        }
        log.error("参数校验异常：{}", message, e);
        return Result.validateFailed(message);
    }

    /**
     * 处理参数绑定异常
     *
     * @param e 参数绑定异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = BindException.class)
    public Result<Void> handleBindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = null;
        if (bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                message = fieldError.getField() + fieldError.getDefaultMessage();
            }
        }
        log.error("参数绑定异常：{}", message, e);
        return Result.validateFailed(message);
    }

    /**
     * 处理其他未知异常
     *
     * @param e 异常
     * @return 返回异常处理结果
     */
    @ExceptionHandler(value = Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常：{}", e.getMessage(), e);
        return Result.failed(ResultCode.FAILED, e.getMessage());
    }
}