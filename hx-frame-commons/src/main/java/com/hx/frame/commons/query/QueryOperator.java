package com.hx.frame.commons.query;

import lombok.Getter;

/**
 * 查询操作符枚举
 * 定义查询条件中支持的操作符
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-23
 */
@Getter
public enum QueryOperator {
    /**
     * 等于
     */
    EQ("eq", "等于"),

    /**
     * 不等于
     */
    NE("ne", "不等于"),

    /**
     * 大于
     */
    GT("gt", "大于"),

    /**
     * 大于等于
     */
    GE("ge", "大于等于"),

    /**
     * 小于
     */
    LT("lt", "小于"),

    /**
     * 小于等于
     */
    LE("le", "小于等于"),

    /**
     * 模糊匹配（包含）
     */
    LIKE("like", "模糊匹配"),

    /**
     * 左模糊匹配（以...结尾）
     */
    LLIKE("llike", "左模糊匹配"),

    /**
     * 右模糊匹配（以...开头）
     */
    RLIKE("rlike", "右模糊匹配"),

    /**
     * 包含于集合
     */
    IN("in", "包含于集合"),

    /**
     * 不包含于集合
     */
    NIN("nin", "不包含于集合"),

    /**
     * 为空
     */
    IS_NULL("isnull", "为空"),

    /**
     * 不为空
     */
    IS_NOT_NULL("isnotnull", "不为空");

    /**
     * 操作符代码
     */
    private final String code;

    /**
     * 操作符描述
     */
    private final String description;

    /**
     * 构造方法
     *
     * @param code        操作符代码
     * @param description 操作符描述
     */
    QueryOperator(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取操作符
     *
     * @param code 操作符代码
     * @return 操作符枚举
     */
    public static QueryOperator fromCode(String code) {
        for (QueryOperator operator : QueryOperator.values()) {
            if (operator.getCode().equals(code)) {
                return operator;
            }
        }
        return EQ; // 默认使用等于操作符
    }
}