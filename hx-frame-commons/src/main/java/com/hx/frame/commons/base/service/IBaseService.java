package com.hx.frame.commons.base.service;

import com.hx.frame.commons.base.entity.BaseEntity;
import com.hx.frame.commons.query.BaseQueryCondition;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 系统基础服务接口，所有服务接口的基类。
 * 定义了实体类的基础CRUD操作方法。
 *
 * @param <T> 实体类型，必须继承自BaseEntity
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
public interface IBaseService<T extends BaseEntity> {

    /**
     * 保存单个实体
     *
     * @param entity 要保存的实体对象
     * @return 保存后的实体对象
     */
    T save(T entity);

    /**
     * 批量保存实体集合
     *
     * @param entities 要保存的实体对象集合
     * @return 保存后的实体对象集合
     */
    Collection<T> saveAll(Collection<T> entities);

    /**
     * 更新单个实体
     *
     * @param entity 要更新的实体对象
     * @return 更新后的实体对象
     */
    T update(T entity);

    /**
     * 批量更新实体集合
     *
     * @param entities 要更新的实体对象集合
     * @return 更新后的实体对象集合
     */
    Collection<T> updateAll(Collection<T> entities);

    /**
     * 根据ID集合进行逻辑删除
     * 将实体的deleted字段设置为true，而不是物理删除数据
     *
     * @param ids 要删除的实体ID集合
     */
    void logicDelete(Collection<String> ids);

    /**
     * 根据ID查询实体
     *
     * @param id 实体ID
     * @return 实体对象
     */
    T findOne(String id);

    /**
     * 查询所有实体
     *
     * @return 实体对象列表
     */
    List<T> findAll();

    /**
     * 按指定排序规则查询所有实体
     *
     * @param sort 排序规则
     * @return 按指定规则排序后的实体对象列表
     */
    List<T> findAll(Sort sort);

    /**
     * 根据查询参数进行分页查询
     *
     * @param params    查询参数
     * @param pageNum   页码
     * @param pageSize  每页记录数
     * @param sortField 排序字段
     * @param sortOrder 排序方式
     * @return 分页结果
     */
    Page<T> findByParams(Map<String, Object> params, Integer pageNum, Integer pageSize,
                         String sortField, String sortOrder);

    /**
     * 根据查询参数和分页参数进行分页查询
     *
     * @param params   查询参数
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<T> findByParams(Map<String, Object> params, Pageable pageable);

    /**
     * 根据查询参数进行列表查询
     *
     * @param params 查询参数
     * @return 查询结果列表
     */
    List<T> findByParams(Map<String, Object> params);

    /**
     * 根据规范进行分页查询
     *
     * @param spec     查询规范
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<T> findBySpec(Specification<T> spec, Pageable pageable);

    /**
     * 根据规范进行列表查询
     *
     * @param spec 查询规范
     * @return 查询结果列表
     */
    List<T> findBySpec(Specification<T> spec);

    /**
     * 根据查询条件进行分页查询
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<T> findByCondition(BaseQueryCondition<T> condition);
}
