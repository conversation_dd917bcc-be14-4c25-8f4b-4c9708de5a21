package com.hx.frame.commons.query;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 查询工具类
 * 提供通用的查询方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-23
 */
public class QueryHelper {

    /**
     * 执行查询（使用自定义规范）
     *
     * @param repository    仓库接口
     * @param condition     查询条件
     * @param specification 自定义查询规范
     * @param <T>           实体类型
     * @param <R>           仓库类型
     * @return 分页结果
     */
    public static <T, R extends JpaSpecificationExecutor<T>> Page<T> query(
            R repository,
            BaseQueryCondition condition,
            Specification<T> specification) {

        // 创建分页对象
        Pageable pageable = condition.toPageable();

        // 执行查询
        return repository.findAll(specification, pageable);
    }

    /**
     * 合并多个查询规范
     *
     * @param specs 查询规范数组
     * @param <T>   实体类型
     * @return 合并后的查询规范
     */
    @SafeVarargs
    public static <T> Specification<T> and(Specification<T>... specs) {
        return (root, query, cb) -> {
            if (specs.length == 0) {
                return null;
            }

            javax.persistence.criteria.Predicate[] predicates = new javax.persistence.criteria.Predicate[specs.length];
            for (int i = 0; i < specs.length; i++) {
                if (specs[i] != null) {
                    predicates[i] = specs[i].toPredicate(root, query, cb);
                }
            }

            return cb.and(predicates);
        };
    }

    /**
     * 合并多个查询规范（或关系）
     *
     * @param specs 查询规范数组
     * @param <T>   实体类型
     * @return 合并后的查询规范
     */
    @SafeVarargs
    public static <T> Specification<T> or(Specification<T>... specs) {
        return (root, query, cb) -> {
            if (specs.length == 0) {
                return null;
            }

            javax.persistence.criteria.Predicate[] predicates = new javax.persistence.criteria.Predicate[specs.length];
            for (int i = 0; i < specs.length; i++) {
                if (specs[i] != null) {
                    predicates[i] = specs[i].toPredicate(root, query, cb);
                }
            }

            return cb.or(predicates);
        };
    }
}
