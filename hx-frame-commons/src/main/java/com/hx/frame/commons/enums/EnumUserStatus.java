package com.hx.frame.commons.enums;

import com.hx.frame.commons.exception.BusinessException;
import lombok.Getter;

/**
 * 用户状态枚举类
 * 定义系统中用户可能的所有状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-26
 */
@Getter
public enum EnumUserStatus {
    /**
     * 禁用状态
     */
    DISABLED(0, "禁用"),

    /**
     * 启用状态
     */
    ENABLED(1, "启用");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code        状态码
     * @param description 状态描述
     */
    EnumUserStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举实例
     *
     * @param code 状态码
     * @return 对应的枚举实例，如果没找到返回null
     */
    public static EnumUserStatus getByCode(Integer code) {
        for (EnumUserStatus status : EnumUserStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new BusinessException("无效的用户状态");
    }

    /**
     * 判断用户是否处于启用状态
     *
     * @param status 用户状态
     * @return 如果用户处于启用状态返回true，否则返回false
     */
    public static boolean isEnabled(Integer status) {
        return ENABLED.getCode().equals(status);
    }

    /**
     * 判断给定的状态码是否有效
     *
     * @param code 状态码
     * @return 如果状态码有效返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}