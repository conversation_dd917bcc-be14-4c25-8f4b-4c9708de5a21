package com.hx.frame.commons.response;

import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应对象
 * 用于包装Spring Data的Page对象，调整页码从0开始为从1开始
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-28
 */
@Data
public class PageResponse<T> {
    /**
     * 当前页码（从1开始）
     */
    private int pageNum;

    /**
     * 每页记录数
     */
    private int pageSize;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int pages;

    /**
     * 当前页的数据列表
     */
    private List<T> list;

    /**
     * 是否为第一页
     */
    private boolean isFirstPage;

    /**
     * 是否为最后一页
     */
    private boolean isLastPage;

    /**
     * 是否有上一页
     */
    private boolean hasPreviousPage;

    /**
     * 是否有下一页
     */
    private boolean hasNextPage;

    /**
     * 将Spring Data的Page对象转换为PageResponse对象
     *
     * @param page Spring Data的Page对象
     * @param <T>  数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> of(Page<T> page) {
        PageResponse<T> response = new PageResponse<>();

        // 调整页码从0开始为从1开始
        response.setPageNum(page.getNumber() + 1);
        response.setPageSize(page.getSize());
        response.setTotal(page.getTotalElements());
        response.setPages(page.getTotalPages());
        response.setList(page.getContent());

        // 设置页面状态
        response.setFirstPage(page.isFirst());
        response.setLastPage(page.isLast());
        response.setHasPreviousPage(page.hasPrevious());
        response.setHasNextPage(page.hasNext());

        return response;
    }
}