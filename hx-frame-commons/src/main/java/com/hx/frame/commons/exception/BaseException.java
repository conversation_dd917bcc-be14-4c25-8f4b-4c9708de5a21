package com.hx.frame.commons.exception;

import lombok.Getter;

/**
 * 系统基础异常类
 * 所有自定义异常的基类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
@Getter
public class BaseException extends RuntimeException {

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param code    错误码
     */
    public BaseException(String message, Integer code) {
        super(message);
        this.code = code;
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param code    错误码
     * @param cause   原始异常
     */
    public BaseException(String message, Integer code, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
}
