package com.hx.frame.commons.query;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * 关联查询条件工具类
 * 用于构建多表关联查询条件
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
public class JoinCriteria<T> {

    private final String joinAttribute;
    private final JoinType joinType;
    private final List<JoinCondition> conditions = new ArrayList<>();
    private final List<JoinCondition> nestedConditions = new ArrayList<>();
    private String nestedJoinAttribute;
    private JoinType nestedJoinType;

    /**
     * 构造函数
     *
     * @param joinAttribute 关联属性名
     * @param joinType      关联类型
     */
    private JoinCriteria(String joinAttribute, JoinType joinType) {
        this.joinAttribute = joinAttribute;
        this.joinType = joinType;
    }

    /**
     * 创建左连接查询条件
     *
     * @param joinAttribute 关联属性名
     * @param <E>           实体类型
     * @return 关联查询条件
     */
    public static <E> JoinCriteria<E> leftJoin(String joinAttribute) {
        return new JoinCriteria<>(joinAttribute, JoinType.LEFT);
    }

    /**
     * 创建内连接查询条件
     *
     * @param joinAttribute 关联属性名
     * @param <E>           实体类型
     * @return 关联查询条件
     */
    public static <E> JoinCriteria<E> innerJoin(String joinAttribute) {
        return new JoinCriteria<>(joinAttribute, JoinType.INNER);
    }

    /**
     * 添加等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 当前对象，用于链式调用
     */
    public JoinCriteria<T> eq(String fieldName, Object value) {
        if (value != null && (!(value instanceof String) || StringUtils.hasText((String) value))) {
            conditions.add(new JoinCondition(fieldName, "EQ", value));
        }
        return this;
    }

    /**
     * 添加模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 当前对象，用于链式调用
     */
    public JoinCriteria<T> like(String fieldName, String value) {
        if (StringUtils.hasText(value)) {
            conditions.add(new JoinCondition(fieldName, "LIKE", value));
        }
        return this;
    }

    /**
     * 添加嵌套关联
     *
     * @param joinAttribute 关联属性名
     * @param joinType      关联类型
     * @return 当前对象，用于链式调用
     */
    public JoinCriteria<T> withNestedJoin(String joinAttribute, JoinType joinType) {
        this.nestedJoinAttribute = joinAttribute;
        this.nestedJoinType = joinType;
        return this;
    }

    /**
     * 添加嵌套关联的等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 当前对象，用于链式调用
     */
    public JoinCriteria<T> nestedEq(String fieldName, Object value) {
        if (value != null && (!(value instanceof String) || StringUtils.hasText((String) value))) {
            nestedConditions.add(new JoinCondition(fieldName, "EQ", value));
        }
        return this;
    }

    /**
     * 添加嵌套关联的模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 当前对象，用于链式调用
     */
    public JoinCriteria<T> nestedLike(String fieldName, String value) {
        if (StringUtils.hasText(value)) {
            nestedConditions.add(new JoinCondition(fieldName, "LIKE", value));
        }
        return this;
    }

    /**
     * 转换为查询规范
     *
     * @return 查询规范
     */
    public Specification<T> toSpecification() {
        return (root, query, cb) -> {
            // 如果没有条件，则不需要关联
            if (conditions.isEmpty() && (nestedJoinAttribute == null || nestedConditions.isEmpty())) {
                return null;
            }

            // 设置查询结果去重
            query.distinct(true);

            // 创建关联
            Join<T, Object> join = root.join(joinAttribute, joinType);
            List<Predicate> predicates = new ArrayList<>();

            // 添加关联条件
            for (JoinCondition condition : conditions) {
                if ("EQ".equals(condition.getOperator())) {
                    predicates.add(cb.equal(join.get(condition.getFieldName()), condition.getValue()));
                } else if ("LIKE".equals(condition.getOperator())) {
                    predicates.add(cb.like(join.get(condition.getFieldName()),
                            "%" + condition.getValue() + "%"));
                }
                // 可以根据需要添加其他操作符的处理
            }

            // 处理嵌套关联
            if (nestedJoinAttribute != null && !nestedConditions.isEmpty()) {
                Join<Object, Object> nestedJoin = join.join(nestedJoinAttribute, nestedJoinType);

                for (JoinCondition condition : nestedConditions) {
                    if ("EQ".equals(condition.getOperator())) {
                        predicates.add(cb.equal(nestedJoin.get(condition.getFieldName()),
                                condition.getValue()));
                    } else if ("LIKE".equals(condition.getOperator())) {
                        predicates.add(cb.like(nestedJoin.get(condition.getFieldName()),
                                "%" + condition.getValue() + "%"));
                    }
                    // 可以根据需要添加其他操作符的处理
                }
            }

            return predicates.isEmpty() ? null : cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 关联条件内部类
     */
    private static class JoinCondition {
        private final String fieldName;
        private final String operator;
        private final Object value;

        public JoinCondition(String fieldName, String operator, Object value) {
            this.fieldName = fieldName;
            this.operator = operator;
            this.value = value;
        }

        public String getFieldName() {
            return fieldName;
        }

        public String getOperator() {
            return operator;
        }

        public Object getValue() {
            return value;
        }
    }
}