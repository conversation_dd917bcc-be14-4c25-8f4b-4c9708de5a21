package com.hx.frame.commons.base.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 系统基础实体类，所有实体类的基类。
 * 提供了实体类共有的基础属性，包括：
 * - 主键ID
 * - 创建人
 * - 创建时间
 * - 更新人
 * - 更新时间
 * - 逻辑删除标记
 * - 版本号（用于乐观锁）
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Data
@MappedSuperclass
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity {

    /**
     * 实体主键ID，使用UUID策略生成
     */
    @Id
    @GeneratedValue(generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String id;

    /**
     * 创建人，由系统自动填充
     */
    @CreatedBy
    private String createdBy;

    /**
     * 创建时间，由系统自动填充
     */
    @CreatedDate
    private LocalDateTime createdTime;

    /**
     * 最后更新人，由系统自动填充
     */
    @LastModifiedBy
    private String updatedBy;

    /**
     * 最后更新时间，由系统自动填充
     */
    @LastModifiedDate
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除标记，默认为false
     */
    @Column(nullable = false)
    @JsonIgnore
    private Boolean deleted = false;

    /**
     * 版本号，用于乐观锁控制，默认为0
     */
    @Version
    private Integer version = 0;
} 