package com.hx.frame.commons.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring上下文持有者
 * 用于在非Spring管理的类中获取Spring Bean
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
@Slf4j
@Component
public class SpringContextHolder implements ApplicationContextAware {

    /**
     * -- GETTER --
     * 获取ApplicationContext
     */
    @Getter
    private static ApplicationContext applicationContext;

    /**
     * 通过类型获取Bean
     *
     * @param clazz Bean类型
     * @param <T>   Bean类型
     * @return Bean实例
     */
    public static <T> T getBean(Class<T> clazz) {
        if (applicationContext == null) {
            throw new IllegalStateException("applicationContext未初始化");
        }
        return applicationContext.getBean(clazz);
    }

    /**
     * 通过名称获取Bean
     *
     * @param name Bean名称
     * @param <T>  Bean类型
     * @return Bean实例
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        if (applicationContext == null) {
            throw new IllegalStateException("applicationContext未初始化");
        }
        return (T) applicationContext.getBean(name);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextHolder.applicationContext = applicationContext;
    }
}