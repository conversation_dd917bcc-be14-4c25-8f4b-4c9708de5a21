package com.hx.frame.commons.base.repository;

import com.hx.frame.commons.base.entity.BaseEntity;
import com.hx.frame.commons.util.PageQueryUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 基础仓库实现类，为系统中所有仓库提供通用实现。
 * 提供了高效的实体新增、更新和逻辑删除功能，使用批处理机制提高大数据量操作的性能。
 *
 * @param <T> 实体类型，必须继承自BaseEntity
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@NoRepositoryBean
public class BaseRepository<T extends BaseEntity> extends SimpleJpaRepository<T, String> implements IBaseRepository<T> {

    /**
     * 默认批处理大小
     */
    private static final int DEFAULT_BATCH_SIZE = 50;
    private final EntityManager entityManager;
    private final JpaEntityInformation<T, String> entityInformation;
    /**
     * 当前使用的批处理大小
     */
    private final int batchSize;

    /**
     * 构造函数
     *
     * @param entityInformation JPA实体信息
     * @param entityManager     实体管理器
     */
    public BaseRepository(JpaEntityInformation<T, String> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.entityManager = entityManager;
        this.entityInformation = entityInformation;

        // 从配置中获取批处理大小，如果未配置则使用默认值
        this.batchSize = DEFAULT_BATCH_SIZE;
    }

    /**
     * 保存新实体。此方法专门用于新增实体，不执行查询操作，提高性能。
     *
     * @param entity 要保存的实体
     * @param <S>    实体类型
     * @return 保存后的实体
     */
    @NotNull
    @Override
    @Transactional
    public <S extends T> S save(S entity) {
        // 仅处理新增操作，不判断是否存在
        entityManager.persist(entity);
        return entity;
    }

    /**
     * 批量保存新实体。通过批处理机制提高大数据量操作的性能。
     *
     * @param entities 要保存的实体集合
     * @param <S>      实体类型
     * @return 保存后的实体列表
     */
    @NotNull
    @Override
    @Transactional
    public <S extends T> List<S> saveAll(Iterable<S> entities) {
        List<S> result = new ArrayList<>();

        if (entities instanceof Collection) {
            int count = 0;

            for (S entity : entities) {
                entityManager.persist(entity);
                result.add(entity);

                // 按批次刷新，提高性能
                if (++count % batchSize == 0) {
                    entityManager.flush();
                    entityManager.clear();
                }
            }

            // 处理剩余的实体
            if (count % batchSize != 0) {
                entityManager.flush();
                entityManager.clear();
            }

            return result;
        } else {
            // 如果不是集合，则转换为集合并递归调用
            List<S> entityList = new ArrayList<>();
            for (S entity : entities) {
                entityList.add(entity);
            }
            return saveAll(entityList);
        }
    }

    /**
     * 更新实体。此方法专门用于更新已存在的实体，不执行查询操作，提高性能。
     *
     * @param entity 要更新的实体
     * @return 更新后的实体
     */
    @Override
    @Transactional
    public T update(T entity) {
        // 直接使用merge更新实体，不进行查询判断
        return entityManager.merge(entity);
    }

    /**
     * 批量更新实体。通过批处理机制提高大数据量操作的性能。
     *
     * @param entities 要更新的实体集合
     * @return 更新后的实体集合
     */
    @Override
    @Transactional
    public Collection<T> updateAll(Collection<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }

        List<T> result = new ArrayList<>(entities.size());
        int count = 0;

        for (T entity : entities) {
            T merged = entityManager.merge(entity);
            result.add(merged);

            // 按批次刷新，提高性能
            if (++count % batchSize == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }

        // 处理剩余的实体
        if (count % batchSize != 0) {
            entityManager.flush();
            entityManager.clear();
        }

        return result;
    }

    /**
     * 逻辑删除实体。通过批处理机制提高大数据量操作的性能。
     * 执行JPQL更新操作将实体的deleted属性设置为true。
     *
     * @param ids 要逻辑删除的实体ID集合
     */
    @Override
    @Transactional
    public void logicDelete(Collection<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 对较大的ID集合进行分批处理
        if (ids.size() > batchSize) {
            List<List<String>> batches = createBatches(ids, batchSize);

            for (List<String> batch : batches) {
                executeLogicDeleteBatch(batch);
            }
        } else {
            executeLogicDeleteBatch(ids);
        }
    }

    /**
     * 执行单个批次的逻辑删除
     *
     * @param ids 要删除的ID集合
     */
    private void executeLogicDeleteBatch(Collection<String> ids) {
        String entityName = entityInformation.getEntityName();
        String updateQuery = String.format("UPDATE %s e SET e.deleted = true WHERE e.id IN :ids", entityName);

        entityManager.createQuery(updateQuery)
                .setParameter("ids", ids)
                .executeUpdate();

        entityManager.flush();
        entityManager.clear();
    }

    /**
     * 将集合分割成指定大小的批次
     *
     * @param collection 原始集合
     * @param batchSize  批次大小
     * @param <E>        集合元素类型
     * @return 分割后的批次列表
     */
    private <E> List<List<E>> createBatches(Collection<E> collection, int batchSize) {
        List<List<E>> batches = new ArrayList<>();
        List<E> currentBatch = new ArrayList<>(batchSize);

        for (E e : collection) {
            currentBatch.add(e);

            if (currentBatch.size() >= batchSize) {
                batches.add(currentBatch);
                currentBatch = new ArrayList<>(batchSize);
            }
        }

        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        return batches;
    }

    /**
     * 根据查询参数进行分页查询
     *
     * @param params   查询参数
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Override
    public Page<T> findByParams(Map<String, Object> params, Pageable pageable) {
        Specification<T> spec = PageQueryUtil.createMapSpecification(params);
        return findAll(spec, pageable);
    }

    /**
     * 根据查询参数进行列表查询
     *
     * @param params 查询参数
     * @return 查询结果列表
     */
    @Override
    public List<T> findByParams(Map<String, Object> params) {
        Specification<T> spec = PageQueryUtil.createMapSpecification(params);
        return findAll(spec);
    }
}
