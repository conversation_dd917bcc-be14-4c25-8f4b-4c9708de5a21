package com.hx.frame.commons.exception;

import com.hx.frame.commons.response.ResultCode;

/**
 * 认证异常类
 * 用于处理认证过程中的异常情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
public class AuthenticationException extends BaseException {

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public AuthenticationException(String message) {
        super(message, ResultCode.UNAUTHORIZED.getCode());
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原始异常
     */
    public AuthenticationException(String message, Throwable cause) {
        super(message, ResultCode.UNAUTHORIZED.getCode(), cause);
    }
}
