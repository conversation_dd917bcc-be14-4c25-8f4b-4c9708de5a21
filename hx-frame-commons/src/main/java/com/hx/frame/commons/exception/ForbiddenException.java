package com.hx.frame.commons.exception;

import com.hx.frame.commons.response.ResultCode;

/**
 * 权限不足异常类
 * 用于处理用户权限不足的情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
public class ForbiddenException extends BaseException {

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public ForbiddenException(String message) {
        super(message, ResultCode.FORBIDDEN.getCode());
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原始异常
     */
    public ForbiddenException(String message, Throwable cause) {
        super(message, ResultCode.FORBIDDEN.getCode(), cause);
    }
}
