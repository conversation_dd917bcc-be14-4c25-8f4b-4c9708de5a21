package com.hx.frame.commons.util;

import com.hx.frame.commons.base.entity.BaseEntity;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import java.util.Map;
import java.util.function.Function;

/**
 * 多表联合查询工具类
 * 提供多表联合查询的通用方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-22
 */
public class JoinQueryUtil {

    /**
     * 创建左连接查询规范
     *
     * @param joinAttribute 关联属性名
     * @param joinParams    关联表查询参数
     * @param <T>           主表实体类型
     * @param <J>           关联表实体类型
     * @return 查询规范
     */
    public static <T extends BaseEntity, J extends BaseEntity> Specification<T> leftJoin(
            String joinAttribute,
            Map<String, Object> joinParams) {

        return (root, query, cb) -> {
            Join<T, J> join = root.join(joinAttribute, JoinType.LEFT);

            return PageQueryUtil.<T>createMapSpecification(joinParams)
                    .toPredicate(root, query, cb);
        };
    }

    /**
     * 创建内连接查询规范
     *
     * @param joinAttribute 关联属性名
     * @param joinParams    关联表查询参数
     * @param <T>           主表实体类型
     * @param <J>           关联表实体类型
     * @return 查询规范
     */
    public static <T extends BaseEntity, J extends BaseEntity> Specification<T> innerJoin(
            String joinAttribute,
            Map<String, Object> joinParams) {

        return (root, query, cb) -> {
            Join<T, J> join = root.join(joinAttribute, JoinType.INNER);

            return PageQueryUtil.<T>createMapSpecification(joinParams)
                    .toPredicate(root, query, cb);
        };
    }

    /**
     * 创建自定义连接查询规范
     *
     * @param joinFunction  连接函数
     * @param whereFunction 条件函数
     * @param <T>           主表实体类型
     * @param <J>           关联表实体类型
     * @return 查询规范
     */
    public static <T extends BaseEntity, J extends BaseEntity> Specification<T> customJoin(
            Function<Root<T>, Join<T, J>> joinFunction,
            Function<Join<T, J>, Specification<T>> whereFunction) {

        return (root, query, cb) -> {
            Join<T, J> join = joinFunction.apply(root);
            return whereFunction.apply(join).toPredicate(root, query, cb);
        };
    }
}