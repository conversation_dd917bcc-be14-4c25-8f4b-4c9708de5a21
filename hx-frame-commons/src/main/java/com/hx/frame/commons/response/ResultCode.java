package com.hx.frame.commons.response;

import lombok.Getter;

/**
 * 统一返回状态码枚举类
 * 定义了系统中所有可能的返回状态码及其对应的提示信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-18
 */
@Getter
public enum ResultCode {
    /**
     * 操作成功
     */
    SUCCESS(200, "操作成功"),
    /**
     * 操作失败
     */
    FAILED(500, "操作失败"),
    /**
     * 参数检验失败
     */
    VALIDATE_FAILED(404, "参数检验失败"),
    /**
     * 暂未登录或token已经过期
     */
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    /**
     * 没有相关权限
     */
    FORBIDDEN(403, "没有相关权限"),
    /**
     * 资源冲突，通常用于乐观锁冲突
     */
    CONFLICT(409, "资源冲突");

    /**
     * 状态码
     */
    private final Integer code;
    /**
     * 提示信息
     */
    private final String message;

    /**
     * 构造方法
     *
     * @param code    状态码
     * @param message 提示信息
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}