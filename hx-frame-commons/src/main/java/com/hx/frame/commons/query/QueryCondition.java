package com.hx.frame.commons.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询条件类
 * 封装字段名、操作符和值
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCondition {
    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 操作符
     */
    private QueryOperator operator;

    /**
     * 值
     */
    private Object value;

    /**
     * 创建等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition eq(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.EQ, value);
    }

    /**
     * 创建不等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition ne(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.NE, value);
    }

    /**
     * 创建大于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition gt(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.GT, value);
    }

    /**
     * 创建大于等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition ge(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.GE, value);
    }

    /**
     * 创建小于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition lt(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.LT, value);
    }

    /**
     * 创建小于等于条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition le(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.LE, value);
    }

    /**
     * 创建模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition like(String fieldName, String value) {
        return new QueryCondition(fieldName, QueryOperator.LIKE, value);
    }

    /**
     * 创建左模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition llike(String fieldName, String value) {
        return new QueryCondition(fieldName, QueryOperator.LLIKE, value);
    }

    /**
     * 创建右模糊匹配条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition rlike(String fieldName, String value) {
        return new QueryCondition(fieldName, QueryOperator.RLIKE, value);
    }

    /**
     * 创建包含于集合条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition in(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.IN, value);
    }

    /**
     * 创建不包含于集合条件
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    public static QueryCondition nin(String fieldName, Object value) {
        return new QueryCondition(fieldName, QueryOperator.NIN, value);
    }

    /**
     * 创建为空条件
     *
     * @param fieldName 字段名
     * @return 查询条件
     */
    public static QueryCondition isNull(String fieldName) {
        return new QueryCondition(fieldName, QueryOperator.IS_NULL, null);
    }

    /**
     * 创建不为空条件
     *
     * @param fieldName 字段名
     * @return 查询条件
     */
    public static QueryCondition isNotNull(String fieldName) {
        return new QueryCondition(fieldName, QueryOperator.IS_NOT_NULL, null);
    }

    /**
     * 获取参数键
     * 格式为：fieldName_operator
     *
     * @return 参数键
     */
    public String getParamKey() {
        return fieldName + "_" + operator.getCode();
    }
}