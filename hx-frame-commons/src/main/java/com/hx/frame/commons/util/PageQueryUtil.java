package com.hx.frame.commons.util;

import com.hx.frame.commons.constant.CommonConstants;
import com.hx.frame.commons.query.QueryOperator;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 分页查询工具类
 * 提供通用的分页查询功能，支持多表联合查询
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-22
 */
@Data
public class PageQueryUtil {

    /**
     * 创建分页请求对象
     *
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param sort     排序
     * @return 分页请求对象
     */
    public static Pageable createPageable(Integer pageNum, Integer pageSize, Sort sort) {
        // 页码从0开始
        pageNum = (pageNum == null || pageNum < 1) ?
                CommonConstants.Pagination.DEFAULT_PAGE_NUMBER - 1 : pageNum - 1;

        // 限制每页记录数
        pageSize = (pageSize == null || pageSize < 1) ?
                CommonConstants.Pagination.DEFAULT_PAGE_SIZE :
                Math.min(pageSize, CommonConstants.Pagination.MAX_PAGE_SIZE);

        return PageRequest.of(pageNum, pageSize, sort != null ? sort : Sort.unsorted());
    }

    /**
     * 创建排序对象
     *
     * @param sortField 排序字段
     * @param sortOrder 排序方式（asc/desc）
     * @return 排序对象
     */
    public static Sort createSort(String sortField, String sortOrder) {
        if (!StringUtils.hasText(sortField)) {
            return Sort.unsorted();
        }

        return "desc".equalsIgnoreCase(sortOrder) ?
                Sort.by(sortField).descending() :
                Sort.by(sortField).ascending();
    }

    /**
     * 创建多表联合查询规范
     *
     * @param rootClass      主表实体类
     * @param joinClass      关联表实体类
     * @param joinCondition  关联条件函数
     * @param whereCondition 查询条件函数
     * @param <T>            主表实体类型
     * @param <J>            关联表实体类型
     * @return 查询规范
     */
    public static <T, J> Specification<T> createJoinSpecification(
            Class<T> rootClass,
            Class<J> joinClass,
            Function<Root<T>, Join<T, J>> joinCondition,
            Function<Join<T, J>, Predicate> whereCondition) {

        return (root, query, cb) -> {
            Join<T, J> join = joinCondition.apply(root);
            return whereCondition.apply(join);
        };
    }

    /**
     * 创建基于Map参数的查询规范
     *
     * @param params 查询参数
     * @param <T>    实体类型
     * @return 查询规范
     */
    public static <T> Specification<T> createMapSpecification(Map<String, Object> params) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            params.forEach((key, value) -> {
                if (value != null && (!(value instanceof String) || StringUtils.hasText((String) value))) {
                    // 处理特殊的查询键
                    if (key.contains("_")) {
                        String[] parts = key.split("_", 2);
                        String fieldName = parts[0];
                        String operatorCode = parts[1].toLowerCase();
                        QueryOperator operator = QueryOperator.fromCode(operatorCode);

                        switch (operator) {
                            case EQ:  // 等于
                                predicates.add(cb.equal(root.get(fieldName), value));
                                break;
                            case NE:  // 不等于
                                predicates.add(cb.notEqual(root.get(fieldName), value));
                                break;
                            case LIKE:  // 模糊匹配
                                predicates.add(cb.like(root.get(fieldName), "%" + value + "%"));
                                break;
                            case LLIKE:  // 左模糊匹配
                                predicates.add(cb.like(root.get(fieldName), "%" + value));
                                break;
                            case RLIKE:  // 右模糊匹配
                                predicates.add(cb.like(root.get(fieldName), value + "%"));
                                break;
                            case GT:  // 大于
                                if (value instanceof Number) {
                                    predicates.add(cb.gt(root.get(fieldName), (Number) value));
                                } else if (value instanceof Comparable) {
                                    predicates.add(cb.greaterThan(root.get(fieldName), (Comparable) value));
                                }
                                break;
                            case GE:  // 大于等于
                                if (value instanceof Number) {
                                    predicates.add(cb.ge(root.get(fieldName), (Number) value));
                                } else if (value instanceof Comparable) {
                                    predicates.add(cb.greaterThanOrEqualTo(root.get(fieldName), (Comparable) value));
                                }
                                break;
                            case LT:  // 小于
                                if (value instanceof Number) {
                                    predicates.add(cb.lt(root.get(fieldName), (Number) value));
                                } else if (value instanceof Comparable) {
                                    predicates.add(cb.lessThan(root.get(fieldName), (Comparable) value));
                                }
                                break;
                            case LE:  // 小于等于
                                if (value instanceof Number) {
                                    predicates.add(cb.le(root.get(fieldName), (Number) value));
                                } else if (value instanceof Comparable) {
                                    predicates.add(cb.lessThanOrEqualTo(root.get(fieldName), (Comparable) value));
                                }
                                break;
                            case IN:  // 包含于集合
                                if (value instanceof Collection) {
                                    predicates.add(root.get(fieldName).in((Collection<?>) value));
                                } else if (value instanceof Object[]) {
                                    predicates.add(root.get(fieldName).in((Object[]) value));
                                } else {
                                    // 单个值当作集合处理
                                    predicates.add(root.get(fieldName).in(value));
                                }
                                break;
                            case NIN:  // 不包含于集合
                                if (value instanceof Collection) {
                                    predicates.add(cb.not(root.get(fieldName).in((Collection<?>) value)));
                                } else if (value instanceof Object[]) {
                                    predicates.add(cb.not(root.get(fieldName).in((Object[]) value)));
                                } else {
                                    // 单个值当作集合处理
                                    predicates.add(cb.not(root.get(fieldName).in(value)));
                                }
                                break;
                            case IS_NULL:  // 为空
                                predicates.add(cb.isNull(root.get(fieldName)));
                                break;
                            case IS_NOT_NULL:  // 不为空
                                predicates.add(cb.isNotNull(root.get(fieldName)));
                                break;
                            default:
                                // 默认使用等于操作
                                predicates.add(cb.equal(root.get(fieldName), value));
                                break;
                        }
                    } else {
                        // 没有指定操作符的情况下，使用等于操作
                        predicates.add(cb.equal(root.get(key), value));
                    }
                }
            });

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}
