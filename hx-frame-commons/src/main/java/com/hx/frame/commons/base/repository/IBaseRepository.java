package com.hx.frame.commons.base.repository;

import com.hx.frame.commons.base.entity.BaseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 系统基础仓库接口，所有仓库接口的基类。
 * 继承自JpaRepository和JpaSpecificationExecutor，提供了基础的CRUD操作和动态查询能力。
 * 同时扩展了更新和逻辑删除等常用功能。
 *
 * @param <T> 实体类型，必须继承自BaseEntity
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@NoRepositoryBean
public interface IBaseRepository<T extends BaseEntity> extends JpaRepository<T, String>, JpaSpecificationExecutor<T> {

    /**
     * 更新单个实体
     *
     * @param entity 要更新的实体对象
     * @return 更新后的实体对象
     */
    T update(T entity);

    /**
     * 批量更新实体集合
     *
     * @param entities 要更新的实体对象集合
     * @return 更新后的实体对象集合
     */
    @Transactional
    Collection<T> updateAll(Collection<T> entities);

    /**
     * 根据ID集合进行逻辑删除
     * 将实体的deleted字段设置为true，而不是物理删除数据
     *
     * @param ids 要删除的实体ID集合
     */
    void logicDelete(Collection<String> ids);

    /**
     * 根据查询参数进行分页查询
     *
     * @param params   查询参数
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<T> findByParams(Map<String, Object> params, Pageable pageable);

    /**
     * 根据查询参数进行列表查询
     *
     * @param params 查询参数
     * @return 查询结果列表
     */
    List<T> findByParams(Map<String, Object> params);
}