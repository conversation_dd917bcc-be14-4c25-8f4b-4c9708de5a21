# HX Framework

HX Framework是一个基于Spring Boot的后台管理系统框架，主要用于系统管理和系统监控。

## 项目结构

项目采用Maven多模块的父子工程结构，按照依赖从低到高的顺序，子模块分别如下：

- hx-frame-util：工具类模块，封装相关工具类
- hx-frame-commons：通用基础层，封装相关通用组件、相关父类
- hx-frame-core：核心层，封装jwt生成、RBAC权限控制相关代码
- hx-frame-report：报表层，封装相关报表、电子表单生成机制
- hx-frame-bootstrap：web启动模块，用于启动后端服务

## 技术栈

- Spring Boot 2.7.18
- Spring Security
- Spring Data JPA
- JWT
- Redis
- MySQL
- Flyway
- Druid
- Lombok
- Hutool

## 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

## 快速开始

1. 克隆项目

```bash
git clone https://github.com/yourusername/hx-frame.git
```

2. 创建数据库

```sql
CREATE DATABASE hx_frame DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

3. 修改配置
   修改 `hx-frame-bootstrap/src/main/resources/application-dev.yml` 中的数据库和Redis配置

4. 启动项目

```bash
mvn spring-boot:run -pl hx-frame-bootstrap
```

## 开发规范

- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 使用JPA进行数据库操作
- 使用Flyway进行数据库版本控制
- 使用JWT进行身份认证
- 使用Redis进行缓存
- 使用Druid进行数据库连接池管理

## 部署

项目支持Docker部署，具体部署方式请参考部署文档。

## 许可证

[MIT](LICENSE)
