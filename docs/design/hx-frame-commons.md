# hx-frame-commons设计文档

本文档描述了hx-frame-commons模块的设计方案。

## 1. 统一异常处理

- 使用SpringBoot进行异常统一处理
- 定义相关接口返回状态
- 接口返回状态需要结合异常信息

## 2. ORM设计

- 生成BaseEntity，必须包含如下属性
    - id: 字符串类型，默认值为uuid
    - createdBy: 字符串类型，存储创建人id
    - createdTime: 时间类型，存储创建时间
    - updatedBy: 字符串类型，存储更新人id
    - updatedTime: 时间类型，存储更新时间
    - deleted: 布尔类型，默认值为false，表示是否删除
    - version: 整形类型，默认值为0，表示版本号，用于乐观锁
- 生成IBaseRepository和BaseRepository，必须实现以下要求
    - 其中, BaseRepository实现IBaseRepository
    - BaseRepository继承SimpleJpaRepository
    - IBaseRepository中需要定义如下方法:
        - update(T entity): 更新实体
        - logicDelete(Collection<String> ids): 逻辑删除
    - BaseRepository中需要定义如下方法:
        - save(T entity): 新增实体, 这里为了提高效率，需要改写JPA默认实现，不需要查询实体判断是新增还是更新
        - saveAll(Collection<T> entities): 批量新增实体, 这里为了提高效率，需要改写JPA默认实现，不需要查询实体判断是新增还是更新，禁止循环中新增
        - update(T entity): 更新实体, 这里为了提高效率，需要改写JPA默认实现，不需要查询实体判断是新增还是更新
        - updateAll(Collection<T> entities): 批量更新实体, 这里为了提高效率，需要改写JPA默认实现，不需要查询实体判断是新增还是更新，禁止循环中更新
        - logicDelete(Collection<String> ids): 逻辑删除
- 生成IBaseService和BaseServiceImpl，必须实现以下要求
    - BaseServiceImpl实现IBaseService
    - IBaseService中需要定义如下方法:
        - save(T entity): 新增实体
        - saveAll(Collection<T> entities): 批量新增实体
        - update(T entity): 更新实体
        - updateAll(Collection<T> entities): 批量更新实体
        - logicDelete(Collection<String> ids): 逻辑删除
        - findById(String id): 根据id查询实体
        - findAll(Pageable pageable): 分页查询实体
        - findAll(Specification<T> spec, Pageable pageable): 分页查询实体
        - findAll(Specification<T> spec): 查询实体
        - findAll(): 查询实体
        - findAll(Sort sort): 查询实体
        - findAll(Sort sort, Pageable pageable): 分页查询实体
- 实现JPA Audit，保证新增和更新实体时，自动从上下文中获取操作人id及操作时间