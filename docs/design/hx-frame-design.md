---
description:
globs:
alwaysApply: true
---

# hx-frame project rules

## 范围

- 这个项目是一个Java后台管理系统
- 主要实现系统管理、系统监控
- 后续的具体项目将使用该项目作为基础框架进行二次开发

## 项目结构

- 采用Maven多模块的父子工程结构
- pom中依赖需要建立统一的版本管理
- 包结构结合传统的MVC结构进行定义
- 资源配置文件使用yml格式

## 注释

- 采用标准的javadoc注释
- 所有的方法和成员变量都需要注释，如果类是接口的实现类，那么只要在接口的方法上实现注释
- 类上面注释格式如下:
  /**

* 类的注释
*
* <AUTHOR>
* @version 1.0.0
* @since YYYY-MM-DD
  **/

## 子模块

按照依赖从低到高的顺序，子模块分别如下:

- hx-frame-util
- hx-frame-commons
- hx-frame-core
- hx-frame-report
- hx-frame-bootstrap

## hx-frame-util模块

该模块是工具类模块，主要封装相关工具类,同时相关底层依赖都在这个模块。以下依赖必须集成进去:

- lombok
- okhttp

## hx-frame-commons模块

该模块是通用基础层，主要封装相关通用组件、相关父类。具体设计文档见 [hx-frame-commons](hx-frame-commons.md)

## hx-frame-core

该模块是核心层，主要封装jwt生成、RBAC权限控制相关代码

## hx-frame-report

该模块是报表层，主要封装相关报表、电子表单生成机制

## hx-frame-bootstrap

该模块是web启动模块，用于启动后端服务。生产application.yml、application-mysql.yml文件，同时yml中使用变量进行配置，方便后续容器化部署。