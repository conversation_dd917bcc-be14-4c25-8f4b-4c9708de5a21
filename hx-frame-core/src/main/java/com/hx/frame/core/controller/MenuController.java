package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.MenuQueryCondition;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.service.MenuService;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 菜单控制器
 * 提供菜单管理相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@RestController
@RequestMapping("/api/menus")
public class MenuController extends BaseController<DtoMenu, MenuService> {

    /**
     * 创建菜单
     * 创建新的菜单记录
     *
     * @param menu 菜单数据对象
     * @return 创建成功的菜单对象
     */
    @PostMapping
    public Result<DtoMenu> create(@RequestBody DtoMenu menu) {
        // 调用服务层保存菜单数据
        return Result.success(service.save(menu));
    }

    /**
     * 更新菜单
     * 更新现有的菜单记录
     *
     * @param menu 菜单数据对象（包含ID）
     * @return 更新后的菜单对象
     */
    @PutMapping
    public Result<DtoMenu> update(@RequestBody DtoMenu menu) {
        // 调用服务层更新菜单数据
        return Result.success(service.update(menu));
    }

    /**
     * 根据ID获取菜单
     * 获取指定ID的菜单详细信息
     *
     * @param id 菜单ID
     * @return 菜单对象
     */
    @GetMapping("/{id}")
    public Result<DtoMenu> getById(@PathVariable("id") String id) {
        // 调用服务层获取菜单数据
        return Result.success(service.findOne(id));
    }

    /**
     * 删除单个菜单
     * 根据ID删除指定的菜单
     *
     * @param id 菜单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(@PathVariable("id") String id) {
        // 调用服务层执行逻辑删除
        service.logicDelete(Collections.singletonList(id));
        return Result.success();
    }

    /**
     * 根据条件获取菜单列表
     * 支持按菜单名称、类型、状态等条件查询
     *
     * @param condition 查询条件
     * @return 菜单列表
     */
    @PostMapping("/tree")
    public Result<List<DtoMenu>> query(@RequestBody MenuQueryCondition condition) {
        // 调用服务层执行查询
        List<DtoMenu> menus = service.getMenuTree(condition);
        return Result.success(menus);
    }


}
