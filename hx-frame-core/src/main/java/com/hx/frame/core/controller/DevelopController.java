package com.hx.frame.core.controller;

import com.hx.frame.core.service.DevelopService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 开发工具 controller
 *
 * <AUTHOR>
 * @version V0.0.1
 * @since 2025/2/1
 */
@RestController
@RequestMapping("/api/develop/tools")
@RequiredArgsConstructor
public class DevelopController {

    private final DevelopService developService;

    /**
     * 生成ApiFox数据模型接口
     *
     * @param params 请求体参数，包含生成ApiFox数据模型所需的键值对
     * @return DtoResponseResult<String> 包含生成结果的响应对象，结果中包含生成的ApiFox数据模型字符串
     */
    @PostMapping("/api-fox/model")
    public String generateApiFoxDataModel(@RequestBody Map<String, String> params) {
        return developService.generateApiFoxDataModel(params);
    }
}
