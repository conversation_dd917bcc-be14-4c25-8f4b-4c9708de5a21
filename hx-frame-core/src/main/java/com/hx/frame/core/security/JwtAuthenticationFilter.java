package com.hx.frame.core.security;

import com.hx.frame.commons.constant.CommonConstants;
import com.hx.frame.commons.constant.RedisConstants;
import com.hx.frame.util.JwtUtils;
import com.hx.frame.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 * 用于处理JWT token的验证和用户认证
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final UserDetailsService userDetailsService;
    private final RedisUtils redisUtils;

    public JwtAuthenticationFilter(UserDetailsService userDetailsService, RedisUtils redisUtils) {
        this.userDetailsService = userDetailsService;
        this.redisUtils = redisUtils;
    }

    /**
     * 处理请求的过滤方法
     * 从请求头中提取JWT令牌，验证并设置安全上下文
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param chain    过滤器链
     * @throws ServletException Servlet异常
     * @throws IOException      IO异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        try {
            // 从请求头中提取Authorization头
            final String authHeader = request.getHeader(CommonConstants.Http.AUTHORIZATION_HEADER);

            // 如果没有Authorization头或格式不正确，直接放行，让Spring Security处理
            if (!StringUtils.hasText(authHeader) || !authHeader.startsWith(CommonConstants.Http.BEARER_PREFIX)) {
                chain.doFilter(request, response);
                return;
            }

            // 提取JWT令牌和用户名
            final String jwt = authHeader.substring(CommonConstants.Http.BEARER_PREFIX.length());
            final String username = JwtUtils.extractUsername(jwt);

            // 如果无法提取用户名，则令牌无效，直接放行
            if (username == null) {
                log.warn("无效的JWT令牌");
                request.setAttribute("authErrorMessage", "无效的JWT令牌");
                chain.doFilter(request, response);
                return;
            }

            // 检查Redis中是否存在对应的访问令牌
            String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
            String storedToken = redisUtils.get(accessTokenKey);

            // 如果Redis中不存在令牌或令牌不匹配，则认证失败
            if (storedToken == null || !storedToken.equals(jwt)) {
                log.warn("令牌已失效或不匹配");
                request.setAttribute("authErrorMessage", "令牌已失效，请重新登录");
                chain.doFilter(request, response);
                return;
            }

            // 如果当前没有认证信息，则进行认证
            if (SecurityContextHolder.getContext().getAuthentication() == null) {
                try {
                    // 加载用户详情
                    UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);

                    // 验证JWT令牌
                    if (JwtUtils.validateToken(jwt, userDetails)) {
                        // 创建认证令牌并设置到安全上下文
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authentication);

                        log.debug("用户[{}]认证成功", username);
                    } else {
                        log.warn("用户[{}]的JWT令牌无效", username);
                        request.setAttribute("authErrorMessage", "JWT令牌验证失败");
                    }
                } catch (Exception e) {
                    log.warn("用户[{}]认证失败: {}", username, e.getMessage());
                    request.setAttribute("authErrorMessage", "用户认证失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("处理JWT认证过程中发生异常: {}", e.getMessage(), e);
            request.setAttribute("authErrorMessage", "认证过程中发生错误: " + e.getMessage());
        }

        // 继续过滤器链，让Spring Security决定是否需要认证
        chain.doFilter(request, response);
    }
}
