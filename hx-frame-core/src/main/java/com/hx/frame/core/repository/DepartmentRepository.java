package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoDepartment;

import java.util.List;

/**
 * 部门仓库接口
 * 提供部门实体的数据访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
public interface DepartmentRepository extends IBaseRepository<DtoDepartment> {

    /**
     * 根据部门名称查询部门
     *
     * @param deptName 部门名称
     * @return 部门对象
     */
    DtoDepartment findByDeptName(String deptName);

    /**
     * 根据父部门ID查询子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<DtoDepartment> findByParentIdOrderBySortAsc(String parentId);

    /**
     * 查询所有部门并按排序值升序排列
     *
     * @return 部门列表
     */
    List<DtoDepartment> findAllByOrderBySortAsc();
}