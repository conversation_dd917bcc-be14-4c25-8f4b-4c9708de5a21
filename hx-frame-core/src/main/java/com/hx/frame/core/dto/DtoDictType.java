package com.hx.frame.core.dto;

import com.hx.frame.core.entity.DictType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

/**
 * 字典类型数据传输对象
 * 用于与前端交互的字典类型数据对象，继承自DictType实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_dict_type")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoDictType extends DictType {

    /**
     * 字典数据列表
     */
    @Transient
    private List<DtoDict> dictList = new ArrayList<>();
}
