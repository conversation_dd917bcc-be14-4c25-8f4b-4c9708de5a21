package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoRole;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 角色查询条件类
 * 用于构建角色查询的条件参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleQueryCondition extends BaseQueryCondition<DtoRole> {

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限字符串
     */
    private String roleKey;

    /**
     * 角色状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 构建查询条件
     * 将查询参数转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 添加角色名称模糊查询条件
        if (StringUtils.hasText(roleName)) {
            like("roleName", roleName);
        }

        // 添加角色权限字符串模糊查询条件
        if (StringUtils.hasText(roleKey)) {
            like("roleKey", roleKey);
        }

        // 添加角色状态精确查询条件
        if (status != null) {
            eq("status", status);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}
