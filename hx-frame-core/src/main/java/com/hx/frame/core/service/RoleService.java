package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;

import java.util.List;

/**
 * 角色服务接口
 * 定义角色相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
public interface RoleService extends IBaseService<DtoRole> {

    /**
     * 获取角色详情，包含菜单权限信息
     *
     * @param id 角色ID
     * @return 角色详情
     */
    DtoRole getRoleWithMenus(String id);

    /**
     * 为角色分配用户
     *
     * @param roleId  角色ID
     * @param userIds 用户ID列表
     */
    void assignUsers(String roleId, List<String> userIds);

    /**
     * 批量移除角色下的用户
     *
     * @param roleId  角色ID
     * @param userIds 用户ID列表
     */
    void removeUsers(String roleId, List<String> userIds);

    /**
     * 获取角色已分配的用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<DtoUser> getUsersByRoleId(String roleId);
}
