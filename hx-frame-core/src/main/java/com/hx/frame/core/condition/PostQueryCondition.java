package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoPost;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 岗位查询条件类
 * 用于封装岗位查询的各种条件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PostQueryCondition extends BaseQueryCondition<DtoPost> {

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 构建查询条件
     * 将属性转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 岗位基本信息
        if (StringUtils.hasText(postName)) {
            like("postName", postName);
        }
        if (StringUtils.hasText(postCode)) {
            like("postCode", postCode);
        }
        if (status != null) {
            eq("status", status);
        }

    }
}
