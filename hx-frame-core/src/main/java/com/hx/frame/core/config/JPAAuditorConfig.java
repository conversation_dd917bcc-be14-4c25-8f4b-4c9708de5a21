package com.hx.frame.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Optional;

/**
 * JPA审计配置类
 * 用于配置JPA审计功能，自动填充创建人、创建时间、更新人、更新时间
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-20
 */
@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
@Slf4j
public class JPAAuditorConfig {

    /**
     * 审计提供者，用于获取当前操作用户
     * 直接从SecurityContext获取用户ID，避免调用UserContext可能导致的循环依赖
     *
     * @return AuditorAware<String> 审计提供者
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            try {
                // 直接从SecurityContext获取用户信息，避免调用UserContext
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null && authentication.isAuthenticated()) {
                    Object principal = authentication.getPrincipal();

                    // 根据Principal的类型获取用户ID
                    if (principal instanceof UserDetails) {
                        return Optional.of(((UserDetails) principal).getUsername());
                    } else if (principal instanceof String) {
                        return Optional.of((String) principal);
                    }
                }
                return Optional.of("system");
            } catch (Exception e) {
                log.debug("获取当前用户ID失败", e);
                return Optional.of("system");
            }
        };
    }
}
