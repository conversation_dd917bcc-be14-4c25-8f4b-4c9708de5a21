package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoDict;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 字典数据查询条件
 * 用于构建字典数据查询的条件参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DictQueryCondition extends BaseQueryCondition<DtoDict> {

    /**
     * 字典类型ID
     */
    private String dictTypeId;

    /**
     * 字典标签
     */
    private String dictLabel;

    /**
     * 字典键值
     */
    private String dictValue;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 构建查询条件
     * 根据查询参数构建JPA查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 添加字典类型ID精确查询条件
        if (StringUtils.hasText(dictTypeId)) {
            eq("dictTypeId", dictTypeId);
        }

        // 添加字典标签模糊查询条件
        if (StringUtils.hasText(dictLabel)) {
            like("dictLabel", dictLabel);
        }

        // 添加字典键值模糊查询条件
        if (StringUtils.hasText(dictValue)) {
            like("dictValue", dictValue);
        }

        // 添加状态精确查询条件
        if (status != null) {
            eq("status", status);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}
