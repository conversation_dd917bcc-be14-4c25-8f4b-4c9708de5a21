package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.PageResponse;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.RoleQueryCondition;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.service.RoleService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 角色控制器
 * 提供角色管理相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@RestController
@RequestMapping("/api/roles")
public class RoleController extends BaseController<DtoRole, RoleService> {

    /**
     * 创建角色
     * 创建新的角色记录
     *
     * @param role 角色数据对象
     * @return 创建成功的角色对象
     */
    @PostMapping
    public Result<DtoRole> create(@RequestBody DtoRole role) {
        // 调用服务层保存角色数据
        return Result.success(service.save(role));
    }

    /**
     * 更新角色
     * 更新现有的角色记录
     *
     * @param role 角色数据对象（包含ID）
     * @return 更新后的角色对象
     */
    @PutMapping
    public Result<DtoRole> update(@RequestBody DtoRole role) {
        // 调用服务层更新角色数据
        return Result.success(service.update(role));
    }

    /**
     * 根据ID获取角色
     * 获取指定ID的角色详细信息
     *
     * @param id 角色ID
     * @return 角色对象
     */
    @GetMapping("/{id}")
    public Result<DtoRole> getById(@PathVariable("id") String id) {
        // 调用服务层获取角色数据，包含菜单权限信息
        DtoRole role = service.getRoleWithMenus(id);
        return Result.success(role);
    }

    /**
     * 批量删除角色
     * 根据ID删除指定的角色
     *
     * @param ids 角色ID集合
     * @return 操作结果
     */
    @DeleteMapping
    public Result<Void> deleteByIds(@RequestBody Collection<String> ids) {
        // 调用服务层执行逻辑删除
        service.logicDelete(ids);
        return Result.success();
    }

    /**
     * 角色查询接口
     * 支持按角色名称模糊查询和状态精确查询
     *
     * @param condition 查询条件
     * @return 角色分页结果
     */
    @PostMapping("/list")
    public Result<PageResponse<DtoRole>> query(@RequestBody RoleQueryCondition condition) {
        // 调用服务层执行查询
        Page<DtoRole> page = service.findByCondition(condition);

        // 将Page对象转换为PageResponse对象
        PageResponse<DtoRole> response = PageResponse.of(page);

        return Result.success(response);
    }

    /**
     * 获取角色已分配的用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    @GetMapping("/{roleId}/users")
    public Result<List<DtoUser>> getRoleUsers(@PathVariable("roleId") String roleId) {
        List<DtoUser> users = service.getUsersByRoleId(roleId);
        return Result.success(users);
    }

    /**
     * 为角色分配用户
     *
     * @param roleId  角色ID
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    @PostMapping("/{roleId}/users")
    public Result<Void> assignUsers(@PathVariable("roleId") String roleId, @RequestBody List<String> userIds) {
        service.assignUsers(roleId, userIds);
        return Result.success();
    }

    /**
     * 批量移除角色下的用户
     *
     * @param roleId  角色ID
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    @DeleteMapping("/{roleId}/users")
    public Result<Void> removeUsers(@PathVariable("roleId") String roleId, @RequestBody List<String> userIds) {
        service.removeUsers(roleId, userIds);
        return Result.success();
    }
}
