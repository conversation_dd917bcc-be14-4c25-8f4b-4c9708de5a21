package com.hx.frame.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 安全相关Bean配置类
 * 用于配置安全相关的Bean，如密码编码器等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-26
 */
@Configuration
public class SecurityBeanConfig {

    /**
     * 配置密码编码器
     * 使用BCrypt加密算法对密码进行加密
     *
     * @return BCrypt密码编码器实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
