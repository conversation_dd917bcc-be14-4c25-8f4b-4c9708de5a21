package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoDictType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 字典类型查询条件
 * 用于构建字典类型查询的条件参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DictTypeQueryCondition extends BaseQueryCondition<DtoDictType> {

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 构建查询条件
     * 根据查询参数构建JPA查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 添加字典名称模糊查询条件
        if (StringUtils.hasText(dictName)) {
            like("dictName", dictName);
        }

        // 添加字典类型模糊查询条件
        if (StringUtils.hasText(dictType)) {
            like("dictType", dictType);
        }

        // 添加状态精确查询条件
        if (status != null) {
            eq("status", status);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}
