package com.hx.frame.core.entity;

import com.hx.frame.commons.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

/**
 * 角色实体类
 * 存储系统角色的基本信息，包括角色名称、角色权限字符串等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class Role extends BaseEntity {

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限字符串
     */
    private String roleKey;

    /**
     * 角色状态（0-禁用，1-启用）
     */
    private Integer status = 1;

    /**
     * 排序值
     */
    private Integer sort = 0;

    /**
     * 备注
     */
    private String remark;
}
