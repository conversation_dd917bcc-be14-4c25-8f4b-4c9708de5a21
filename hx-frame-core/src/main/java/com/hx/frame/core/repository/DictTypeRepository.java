package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoDictType;

/**
 * 字典类型仓库接口
 * 提供字典类型实体的数据访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
public interface DictTypeRepository extends IBaseRepository<DtoDictType> {

    /**
     * 根据字典类型查询字典类型对象
     *
     * @param dictType 字典类型
     * @return 字典类型对象
     */
    DtoDictType findByDictType(String dictType);

}
