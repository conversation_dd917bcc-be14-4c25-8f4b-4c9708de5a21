package com.hx.frame.core.dto;

import com.hx.frame.core.entity.Post;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 岗位数据传输对象
 * 用于岗位数据的传输和展示
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "hx_frame_post")
public class DtoPost extends Post {

    /**
     * 状态名称
     */
    @Transient
    private transient String statusName;

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    public String getStatusName() {
        return getStatus() != null ? (getStatus() == 1 ? "启用" : "禁用") : "";
    }
}
