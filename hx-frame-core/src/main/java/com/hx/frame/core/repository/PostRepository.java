package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoPost;
import org.springframework.stereotype.Repository;

/**
 * 岗位仓库接口
 * 提供岗位数据的访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-17
 */
@Repository
public interface PostRepository extends IBaseRepository<DtoPost> {

    /**
     * 根据岗位名称查询岗位
     *
     * @param postName 岗位名称
     * @return 岗位对象
     */
    DtoPost findByPostName(String postName);

    /**
     * 根据岗位编码查询岗位
     *
     * @param postCode 岗位编码
     * @return 岗位对象
     */
    DtoPost findByPostCode(String postCode);
}
