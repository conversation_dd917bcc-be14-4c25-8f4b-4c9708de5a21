package com.hx.frame.core.config;

import com.hx.frame.util.JwtUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * JWT配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "jwt")
public class JwtConfig {
    /**
     * JWT密钥
     */
    private String secret;

    /**
     * 访问令牌有效期（分钟）
     */
    private long accessTokenValidity;

    /**
     * 刷新令牌有效期（分钟）
     */
    private long refreshTokenValidity;

    @PostConstruct
    public void init() {
        JwtUtils.init(secret, accessTokenValidity, refreshTokenValidity);
    }
}