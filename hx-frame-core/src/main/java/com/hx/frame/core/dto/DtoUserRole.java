package com.hx.frame.core.dto;

import com.hx.frame.core.entity.UserRole;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 用户角色关联数据传输对象
 * 用于与前端交互的用户角色关联数据对象，继承自UserRole实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_user_role")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoUserRole extends UserRole {

    /**
     * 用户名称
     */
    @Transient
    private String userName;

    /**
     * 角色名称
     */
    @Transient
    private String roleName;
}
