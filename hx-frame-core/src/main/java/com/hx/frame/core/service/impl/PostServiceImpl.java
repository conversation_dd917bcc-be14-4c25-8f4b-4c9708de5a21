package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.dto.DtoPost;
import com.hx.frame.core.repository.PostRepository;
import com.hx.frame.core.service.PostService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 岗位服务实现类
 * 实现岗位相关的业务逻辑
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-17
 */
@Slf4j
@Service
public class PostServiceImpl extends BaseServiceImpl<DtoPost, PostRepository> implements PostService {

    /**
     * 重写保存方法，添加岗位名称和编码唯一性检查
     *
     * @param post 岗位实体
     * @return 保存后的岗位实体
     */
    @Override
    @Transactional
    public DtoPost save(DtoPost post) {
        // 检查岗位名称是否唯一
        if (!checkPostNameUnique(post.getPostName(), null).getData()) {
            throw new BusinessException("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        }

        // 检查岗位编码是否唯一
        if (!checkPostCodeUnique(post.getPostCode(), null).getData()) {
            throw new BusinessException("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }

        return super.save(post);
    }

    /**
     * 重写更新方法，添加岗位名称和编码唯一性检查
     *
     * @param post 岗位实体
     * @return 更新后的岗位实体
     */
    @Override
    @Transactional
    public DtoPost update(DtoPost post) {
        // 检查岗位名称是否唯一
        if (!checkPostNameUnique(post.getPostName(), post.getId()).getData()) {
            throw new BusinessException("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        }

        // 检查岗位编码是否唯一
        if (!checkPostCodeUnique(post.getPostCode(), post.getId()).getData()) {
            throw new BusinessException("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }

        return super.update(post);
    }

    /**
     * 检查岗位名称是否唯一
     *
     * @param postName 岗位名称
     * @param id       岗位ID（更新时使用）
     * @return 结果
     */
    private Result<Boolean> checkPostNameUnique(String postName, String id) {
        DtoPost post = repository.findByPostName(postName);
        if (post == null) {
            return Result.success(true);
        }

        // 如果是更新操作，且查询到的是当前记录，则认为名称唯一
        if (id != null && Objects.equals(post.getId(), id)) {
            return Result.success(true);
        }

        return Result.success(false);
    }

    /**
     * 检查岗位编码是否唯一
     *
     * @param postCode 岗位编码
     * @param id       岗位ID（更新时使用）
     * @return 结果
     */
    private Result<Boolean> checkPostCodeUnique(String postCode, String id) {
        DtoPost post = repository.findByPostCode(postCode);
        if (post == null) {
            return Result.success(true);
        }

        // 如果是更新操作，且查询到的是当前记录，则认为编码唯一
        if (id != null && Objects.equals(post.getId(), id)) {
            return Result.success(true);
        }

        return Result.success(false);
    }
}
