package com.hx.frame.core.entity;

import com.hx.frame.commons.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

/**
 * 岗位实体类
 * 存储系统岗位的基本信息，包括岗位名称、岗位编码等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class Post extends BaseEntity {

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 岗位状态：0-禁用，1-启用
     */
    private Integer status = 1;

    /**
     * 排序值
     */
    private Integer sort = 0;

    /**
     * 备注
     */
    private String remark;
}
