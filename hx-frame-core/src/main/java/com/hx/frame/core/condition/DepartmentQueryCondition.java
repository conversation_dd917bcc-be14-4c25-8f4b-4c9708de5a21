package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoDepartment;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 部门查询条件类
 * 用于封装部门查询的各种条件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DepartmentQueryCondition extends BaseQueryCondition<DtoDepartment> {

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门状态
     */
    private Integer status;

    /**
     * 父部门ID
     */
    private String parentId;

    /**
     * 构建查询条件
     * 将属性转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 部门名称模糊查询
        if (StringUtils.hasText(deptName)) {
            like("deptName", deptName);
        }

        // 部门状态精确查询
        if (status != null) {
            eq("status", status);
        }

        // 父部门ID精确查询
        if (StringUtils.hasText(parentId)) {
            eq("parentId", parentId);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}