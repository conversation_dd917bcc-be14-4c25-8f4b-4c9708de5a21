package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.DictQueryCondition;
import com.hx.frame.core.dto.DtoDict;
import com.hx.frame.core.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 字典数据管理控制器
 * 提供字典数据相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@Slf4j
@RestController
@RequestMapping("/api/dict/data")
public class DictController extends BaseController<DtoDict, DictService> {

    /**
     * 根据条件查询字典数据列表
     *
     * @param condition 查询条件
     * @return 字典数据分页结果
     */
    @PostMapping("/list")
    public Result<Page<DtoDict>> list(@RequestBody DictQueryCondition condition) {
        Page<DtoDict> result = service.findByCondition(condition);
        return Result.success(result);
    }

    /**
     * 根据字典类型ID获取字典数据列表
     *
     * @param dictTypeId 字典类型ID
     * @return 字典数据列表
     */
    @GetMapping("/type/{dictTypeId}")
    public Result<List<DtoDict>> getByDictTypeId(@PathVariable("dictTypeId") String dictTypeId) {
        List<DtoDict> dicts = service.getDictsByDictTypeId(dictTypeId);
        return Result.success(dicts);
    }

    /**
     * 根据字典类型编码获取字典数据列表
     *
     * @param dictType 字典类型编码
     * @return 字典数据列表
     */
    @GetMapping("/code/{dictType}")
    public Result<List<DtoDict>> getByDictType(@PathVariable("dictType") String dictType) {
        List<DtoDict> dicts = service.getDictsByDictType(dictType);
        return Result.success(dicts);
    }

    /**
     * 根据ID获取字典数据
     *
     * @param id 字典数据ID
     * @return 字典数据对象
     */
    @GetMapping("/{id}")
    public Result<DtoDict> getById(@PathVariable("id") String id) {
        DtoDict dict = service.findOne(id);
        return Result.success(dict);
    }

    /**
     * 新增字典数据
     *
     * @param dict 字典数据信息
     * @return 操作结果
     */
    @PostMapping
    public Result<DtoDict> add(@RequestBody DtoDict dict) {
        DtoDict savedDict = service.save(dict);
        return Result.success(savedDict);
    }

    /**
     * 更新字典数据
     *
     * @param dict 字典数据信息
     * @return 操作结果
     */
    @PutMapping
    public Result<DtoDict> update(@RequestBody DtoDict dict) {
        DtoDict updatedDict = service.update(dict);
        return Result.success(updatedDict);
    }

    /**
     * 批量删除字典数据
     *
     * @param ids 字典数据ID数组
     * @return 操作结果
     */
    @DeleteMapping
    public Result<Void> batchDelete(@RequestBody Collection<String> ids) {
        service.logicDelete(ids);
        return Result.success();
    }

}
