package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoUser;

/**
 * 用户仓库接口
 * 提供用户实体的数据访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
public interface UserRepository extends IBaseRepository<DtoUser> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象
     */
    DtoUser findByUsername(String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户对象
     */
    DtoUser findByEmail(String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户对象
     */
    DtoUser findByPhone(String phone);
} 