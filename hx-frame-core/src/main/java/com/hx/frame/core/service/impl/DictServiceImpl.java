package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.dto.DtoDict;
import com.hx.frame.core.dto.DtoDictType;
import com.hx.frame.core.repository.DictRepository;
import com.hx.frame.core.repository.DictTypeRepository;
import com.hx.frame.core.service.DictService;
import com.hx.frame.core.service.DictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 字典数据服务实现类
 * 实现字典数据相关的业务逻辑
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@Slf4j
@Service
public class DictServiceImpl extends BaseServiceImpl<DtoDict, DictRepository> implements DictService {

    private final DictTypeRepository dictTypeRepository;

    private final DictTypeService dictTypeService;

    @Autowired
    public DictServiceImpl(DictTypeRepository dictTypeRepository,
                           DictTypeService dictTypeService) {
        this.dictTypeRepository = dictTypeRepository;
        this.dictTypeService = dictTypeService;
    }

    /**
     * 重写保存方法，添加字典标签和键值唯一性检查
     *
     * @param dict 字典数据实体
     * @return 保存后的字典数据实体
     */
    @Override
    @Transactional
    public DtoDict save(DtoDict dict) {
        // 检查字典键值是否唯一
        if (!checkDictValueUnique(dict.getDictValue(), dict.getDictTypeId(), null).getData()) {
            throw new BusinessException("新增字典数据'" + dict.getDictLabel() + "'失败，字典键值已存在");
        }

        // 设置字典类型信息
        setDictTypeInfo(dict);

        return super.save(dict);
    }

    /**
     * 重写更新方法，添加字典标签和键值唯一性检查
     *
     * @param dict 字典数据实体
     * @return 更新后的字典数据实体
     */
    @Override
    @Transactional
    public DtoDict update(DtoDict dict) {
        // 检查字典键值是否唯一
        if (!checkDictValueUnique(dict.getDictValue(), dict.getDictTypeId(), dict.getId()).getData()) {
            throw new BusinessException("修改字典数据'" + dict.getDictLabel() + "'失败，字典键值已存在");
        }

        // 设置字典类型信息
        setDictTypeInfo(dict);

        return super.update(dict);
    }

    /**
     * 根据字典类型ID获取字典数据列表
     *
     * @param dictTypeId 字典类型ID
     * @return 字典数据列表
     */
    @Override
    public List<DtoDict> getDictsByDictTypeId(String dictTypeId) {
        return repository.findByDictTypeIdOrderBySortAsc(dictTypeId);
    }


    @Override
    public List<DtoDict> getDictsByDictType(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            return new ArrayList<>();
        }

        // 获取字典类型对象
        DtoDictType dictTypeObj = dictTypeService.getDictTypeByType(dictType);
        if (dictTypeObj == null) {
            return new ArrayList<>();
        }

        // 获取字典数据
        return getDictsByDictTypeId(dictTypeObj.getId());
    }

    @Override
    public List<DtoDict> findByDictTypeIds(Collection<String> dictTypeIds) {
        return Collections.emptyList();
    }

    /**
     * 设置字典类型信息
     *
     * @param dict 字典数据实体
     */
    private void setDictTypeInfo(DtoDict dict) {
        if (dict != null && dict.getDictTypeId() != null) {
            DtoDictType dictType = dictTypeRepository.findById(dict.getDictTypeId()).orElseThrow(() -> new BusinessException("字典类型不存在"));
            dict.setDictType(dictType.getDictType());
        }
    }

    /**
     * 检查字典键值是否唯一
     *
     * @param dictValue  字典键值
     * @param dictTypeId 字典类型ID
     * @param id         字典数据ID（更新时使用）
     * @return 结果
     */
    private Result<Boolean> checkDictValueUnique(String dictValue, String dictTypeId, String id) {
        DtoDict dict = repository.findByDictTypeIdAndDictValue(dictTypeId, dictValue);
        if (dict == null) {
            return Result.success(true);
        }

        // 如果是更新操作，且查询到的是当前记录，则认为键值唯一
        if (id != null && Objects.equals(dict.getId(), id)) {
            return Result.success(true);
        }

        return Result.success(false);
    }
}
