package com.hx.frame.core.service.impl;

import com.hx.frame.commons.constant.RedisConstants;
import com.hx.frame.commons.constant.TreeConstants;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.commons.response.AuthResponse;
import com.hx.frame.core.config.JwtConfig;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.dto.DtoRole;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.security.UserContext;
import com.hx.frame.core.service.AuthService;
import com.hx.frame.core.service.RoleMenuService;
import com.hx.frame.core.service.UserService;
import com.hx.frame.core.vo.UserInfoVO;
import com.hx.frame.util.JwtUtils;
import com.hx.frame.util.RedisUtils;
import com.hx.frame.util.tree.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserDetailsService userDetailsService;
    private final RedisUtils redisUtils;
    private final JwtConfig jwtConfig;
    private final UserService userService;
    private final RoleMenuService roleMenuService;

    public AuthServiceImpl(AuthenticationManager authenticationManager,
                           UserDetailsService userDetailsService,
                           RedisUtils redisUtils,
                           JwtConfig jwtConfig,
                           UserService userService,
                           RoleMenuService roleMenuService) {
        this.authenticationManager = authenticationManager;
        this.userDetailsService = userDetailsService;
        this.redisUtils = redisUtils;
        this.jwtConfig = jwtConfig;
        this.userService = userService;
        this.roleMenuService = roleMenuService;
    }

    @Override
    public AuthResponse login(String username, String password) {
        try {
            log.debug("用户开始登录: {}", username);

            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password)
            );

            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            log.debug("用户登录成功: {}", username);

            String accessToken = JwtUtils.generateAccessToken(userDetails);
            String refreshToken = JwtUtils.generateRefreshToken(userDetails);

            // 将刷新令牌存储到Redis
            String refreshTokenKey = RedisConstants.REFRESH_TOKEN_PREFIX + username;
            redisUtils.set(refreshTokenKey, refreshToken, jwtConfig.getRefreshTokenValidity(), TimeUnit.MINUTES);

            // 将访问令牌也存储到Redis
            String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
            redisUtils.set(accessTokenKey, accessToken, jwtConfig.getAccessTokenValidity(), TimeUnit.MINUTES);

            return new AuthResponse(accessToken, refreshToken, JwtUtils.TOKEN_TYPE_BEARER);
        } catch (AuthenticationException e) {
            log.error("用户登录失败: {}", username);
            throw new BusinessException("用户名或密码错误");
        }
    }

    @Override
    public AuthResponse refresh(String refreshToken) {
        String username = JwtUtils.extractUsername(refreshToken);
        if (username == null) {
            throw new BusinessException("无效的刷新令牌");
        }

        String refreshTokenKey = RedisConstants.REFRESH_TOKEN_PREFIX + username;
        String storedRefreshToken = redisUtils.get(refreshTokenKey);

        if (storedRefreshToken == null || !storedRefreshToken.equals(refreshToken)) {
            throw new BusinessException("刷新令牌已失效");
        }

        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        String newAccessToken = JwtUtils.generateAccessToken(userDetails);

        // 更新刷新令牌的过期时间
        redisUtils.expire(refreshTokenKey, jwtConfig.getRefreshTokenValidity(), TimeUnit.MINUTES);

        // 更新Redis中的访问令牌
        String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
        redisUtils.set(accessTokenKey, newAccessToken, jwtConfig.getAccessTokenValidity(), TimeUnit.MINUTES);

        return new AuthResponse(newAccessToken, refreshToken, JwtUtils.TOKEN_TYPE_BEARER);
    }

    @Override
    public void logout(String token) {
        // 从令牌中提取用户名
        String username = JwtUtils.extractUsername(token);
        if (username == null) {
            log.warn("注销失败：无效的令牌");
            return;
        }

        // 删除Redis中存储的刷新令牌
        String refreshTokenKey = RedisConstants.REFRESH_TOKEN_PREFIX + username;
        redisUtils.delete(refreshTokenKey);

        // 删除Redis中存储的访问令牌
        String accessTokenKey = RedisConstants.ACCESS_TOKEN_PREFIX + username;
        redisUtils.delete(accessTokenKey);

        log.info("用户 {} 已成功注销", username);
    }

    @Override
    public UserInfoVO loadCurrentUser() {
        DtoUser user = userService.getUserWithRoles(UserContext.getCurrentUserId());
        return UserInfoVO.builder()
                .id(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .phone(user.getPhone())
                .email(user.getEmail())
                .status(user.getStatus().toString())
                .deptId(user.getDepartmentId())
                .postId(user.getPostId())
                .avatar(user.getAvatar())
                .roleList(user.getRoles())
                .roleCodes(user.getRoles().stream()
                        .sorted(Comparator.comparing(DtoRole::getSort))
                        .map(DtoRole::getRoleKey)
                        .collect(Collectors.toList()))
                .build();
    }

    @Override
    public List<DtoMenu> getCurrentUserMenus() {
        // 获取当前用户ID
        String currentUserId = UserContext.getCurrentUserId();

        // 获取用户信息和角色
        DtoUser user = userService.getUserWithRoles(currentUserId);

        // 获取所有菜单信息
        List<DtoMenu> allMenus = roleMenuService.getMenusByRoleIds(user.getRoleIds());

        // 去重并按排序值排序
        List<DtoMenu> uniqueMenus = allMenus.stream()
                .sorted(Comparator.comparing(DtoMenu::getSort))
                .collect(Collectors.toList());

        // 构建树形结构
        return TreeUtils.buildTree(uniqueMenus, TreeConstants.ROOT_PARENT_ID);
    }
}
