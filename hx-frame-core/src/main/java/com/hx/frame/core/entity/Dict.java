package com.hx.frame.core.entity;

import com.hx.frame.commons.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

/**
 * 字典数据实体类
 * 存储系统字典数据的基本信息，包括字典标签、字典键值等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class Dict extends BaseEntity {

    /**
     * 字典类型ID
     */
    private String dictTypeId;

    /**
     * 字典标签
     */
    private String dictLabel;

    /**
     * 字典键值
     */
    private String dictValue;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status = 1;

    /**
     * 排序值
     */
    private Integer sort = 0;

    /**
     * 备注
     */
    private String remark;
}
