package com.hx.frame.core.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hx.frame.commons.exception.AuthenticationException;
import com.hx.frame.commons.response.Result;
import com.hx.frame.commons.response.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证异常处理过滤器
 * 用于捕获并处理JWT认证过程中的异常
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Slf4j
public class JwtAuthenticationExceptionFilter extends OncePerRequestFilter {

    private final ObjectMapper objectMapper;

    public JwtAuthenticationExceptionFilter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            log.error("过滤器链执行异常: {}", e.getMessage(), e);
            handleException(request, response, e);
        }

        // 检查请求属性中是否有认证异常
        Object exceptionAttr = request.getAttribute("authenticationException");
        if (exceptionAttr instanceof AuthenticationException) {
            handleException(request, response, (AuthenticationException) exceptionAttr);
        }
    }

    /**
     * 处理异常并返回JSON响应
     *
     * @param request   请求对象
     * @param response  响应对象
     * @param exception 异常对象
     * @throws IOException IO异常
     */
    private void handleException(HttpServletRequest request, HttpServletResponse response, Exception exception)
            throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        Result<?> result;
        if (exception instanceof AuthenticationException) {
            result = Result.failed(ResultCode.UNAUTHORIZED, exception.getMessage());
        } else {
            result = Result.failed(ResultCode.UNAUTHORIZED, "认证失败");
        }

        objectMapper.writeValue(response.getOutputStream(), result);
    }
}