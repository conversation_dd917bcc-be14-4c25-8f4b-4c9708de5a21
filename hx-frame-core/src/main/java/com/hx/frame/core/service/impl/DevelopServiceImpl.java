package com.hx.frame.core.service.impl;

import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.service.DevelopService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.Table;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 开发工具服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/2/1
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DevelopServiceImpl implements DevelopService {

    private final JdbcTemplate jdbcTemplate;

    @Override
    public String generateApiFoxDataModel(Map<String, String> params) {
        String modelName = params.get("modelName");
        String className = params.get("className");
        String schemaName = params.get("schemaName");

        if (!StringUtils.hasText(className)) {
            throw new BusinessException("类名不能为空");
        }

        try {
            Class<?> clazz = Class.forName(className);

            // 获取表注解信息
            Table tableAnnotation = clazz.getAnnotation(Table.class);
            if (tableAnnotation == null) {
                throw new BusinessException("类" + className + "未标注@Table注解");
            }

            String tableName = tableAnnotation.name();

            // 获取表中已有属性
            List<FieldVO> tableFields = getTableFieldVoList(schemaName, tableName);
            Set<String> tableFieldNames = tableFields.stream()
                    .map(FieldVO::getFieldName)
                    .collect(Collectors.toSet());

            // 获取实体类中的附加属性
            List<FieldVO> entityFields = getEntityAdditionalFields(clazz, tableFieldNames);

            // 合并所有字段
            List<FieldVO> allFields = new ArrayList<>(tableFields);
            allFields.addAll(entityFields);

            return generateJsonSchema(modelName, allFields);
        } catch (ClassNotFoundException e) {
            log.error("找不到指定的类: {}", className, e);
            throw new BusinessException("找不到指定的类: " + className);
        } catch (Exception e) {
            log.error("生成ApiFox数据模型出错", e);
            throw new BusinessException("生成ApiFox数据模型出错: " + e.getMessage());
        }
    }

    /**
     * 获取实体类中的附加属性（不在数据库表中的属性）
     *
     * @param clazz           实体类
     * @param tableFieldNames 表中已有的字段名集合
     * @return 附加属性列表
     */
    private List<FieldVO> getEntityAdditionalFields(Class<?> clazz, Set<String> tableFieldNames) {
        List<Field> allFields = new ArrayList<>();
        getAllFields(allFields, clazz);

        return allFields.stream()
                .filter(field -> !tableFieldNames.contains(field.getName()))
                .map(field -> generateFieldObjectForEntity(
                        field.getName(),
                        field.getAnnotatedType().getType().getTypeName()))
                .filter(vo -> !"object".equals(vo.getFieldType()))
                .collect(Collectors.toList());
    }

    /**
     * 递归获取类的所有字段，包括父类字段
     *
     * @param fields 字段列表
     * @param clazz  类
     */
    private void getAllFields(List<Field> fields, Class<?> clazz) {
        if (clazz == null || clazz == Object.class) {
            return;
        }

        fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        getAllFields(fields, clazz.getSuperclass());
    }

    /**
     * 将数据库字段名（大写带下划线）转换为小驼峰命名法
     *
     * @param fieldName 数据库字段名
     * @return 转换后的小驼峰命名法字符串
     */
    private String convertToCamelCase(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return fieldName;
        }

        String[] parts = fieldName.split("_");
        StringBuilder camelCaseName = new StringBuilder(parts[0].toLowerCase());

        for (int i = 1; i < parts.length; i++) {
            String part = parts[i].toLowerCase();
            if (!part.isEmpty()) {
                camelCaseName.append(Character.toUpperCase(part.charAt(0)))
                        .append(part.substring(1));
            }
        }

        return camelCaseName.toString();
    }

    /**
     * 获取数据库表字段信息
     *
     * @param schemaName 模式名
     * @param tableName  表名
     * @return 字段信息列表
     */
    private List<FieldVO> getTableFieldVoList(String schemaName, String tableName) {
        List<FieldVO> voList = new ArrayList<>();

        try (Connection conn = Objects.requireNonNull(jdbcTemplate.getDataSource()).getConnection()) {
            DatabaseMetaData dbMetaData = conn.getMetaData();
            String[] types = {"TABLE"};

            try (ResultSet tableRs = dbMetaData.getTables(null, schemaName, tableName, types)) {
                while (tableRs.next()) {
                    String actualTableName = tableRs.getString("TABLE_NAME");

                    try (ResultSet columnRs = dbMetaData.getColumns(null, null, actualTableName, null)) {
                        Set<String> processedColumns = new HashSet<>();

                        while (columnRs.next()) {
                            String columnName = convertToCamelCase(columnRs.getString("COLUMN_NAME"));

                            if (!processedColumns.contains(columnName)) {
                                String columnType = columnRs.getString("TYPE_NAME");
                                String columnRemarks = columnRs.getString("REMARKS");

                                voList.add(generateFieldObject(columnName, columnType, columnRemarks));
                                processedColumns.add(columnName);
                            }
                        }
                    }
                }
            }

            return voList;
        } catch (SQLException e) {
            log.error("获取数据库表字段信息出错", e);
            throw new BusinessException("获取数据库表字段信息出错: " + e.getMessage());
        }
    }

    /**
     * 生成ApiFox JSON Schema
     *
     * @param modelName 模型名称
     * @param fields    字段列表
     * @return JSON Schema字符串
     */
    private String generateJsonSchema(String modelName, List<FieldVO> fields) {
        StringBuilder sb = new StringBuilder();
        sb.append("{")
                .append("\"type\": \"object\",");

        // 属性定义
        sb.append("\"properties\": {");
        String propertiesJson = fields.stream()
                .map(FieldVO::toString)
                .collect(Collectors.joining(","));
        sb.append(propertiesJson);
        sb.append("},");

        // 字段顺序
        List<String> fieldNames = fields.stream()
                .map(FieldVO::fieldWithDoubleQuote)
                .collect(Collectors.toList());
        sb.append("\"x-apifox-orders\": [")
                .append(String.join(",", fieldNames))
                .append("],");

        // 标题
        sb.append("\"title\": \"").append(modelName).append("\",");

        // 必填字段
        sb.append("\"required\": [")
                .append(String.join(",", fieldNames))
                .append("]");

        sb.append("}");
        return sb.toString();
    }

    /**
     * 根据数据库字段信息生成FieldVO对象
     *
     * @param columnName    字段名
     * @param columnType    字段类型
     * @param columnRemarks 字段注释
     * @return FieldVO对象
     */
    private FieldVO generateFieldObject(String columnName, String columnType, String columnRemarks) {
        FieldVO vo = new FieldVO();
        vo.setFieldName(columnName);
        vo.setFieldDesc(columnRemarks);

        if (columnType.startsWith("VARCHAR") || columnType.startsWith("NVARCHAR")) {
            vo.setFieldType("string");
            vo.setMockValue(columnName.endsWith("id") ? "@guid" : "@word");
        } else if (columnType.startsWith("INT") || columnType.endsWith("INT")) {
            vo.setFieldType("integer");
            vo.setMockValue("@integer(60, 100)");
        } else if (columnType.startsWith("BIT")) {
            vo.setFieldType("boolean");
            vo.setMockValue("@boolean");
        } else if (columnType.startsWith("DECIMAL")) {
            vo.setFieldType("number");
            vo.setMockValue("@float(60, 100, 3,2)");
        } else if ("DATE".equals(columnType)) {
            vo.setFieldType("string");
            vo.setMockValue("@date");
        } else if ("DATETIME".equals(columnType)) {
            vo.setFieldType("string");
            vo.setMockValue("@datetime");
        } else if (columnType.endsWith("TEXT")) {
            vo.setFieldType("string");
            vo.setMockValue("@csentence");
        } else {
            log.warn("未知的数据库字段类型: {}", columnType);
            vo.setFieldType("string");
            vo.setMockValue("@word");
        }

        return vo;
    }

    /**
     * 根据Java类型生成FieldVO对象
     *
     * @param columnName 字段名
     * @param columnType Java类型全限定名
     * @return FieldVO对象
     */
    private FieldVO generateFieldObjectForEntity(String columnName, String columnType) {
        FieldVO vo = new FieldVO();
        vo.setFieldName(columnName);
        vo.setFieldDesc("");

        String shortType = extractShortTypeName(columnType);

        switch (shortType) {
            case "String":
                vo.setFieldType("string");
                vo.setMockValue("@word");
                break;
            case "Integer":
            case "Long":
            case "Short":
            case "Byte":
                vo.setFieldType("integer");
                vo.setMockValue("@integer(60, 100)");
                break;
            case "Boolean":
                vo.setFieldType("boolean");
                vo.setMockValue("@boolean");
                break;
            case "BigDecimal":
            case "Float":
            case "Double":
                vo.setFieldType("number");
                vo.setMockValue("@float(60, 100, 3,2)");
                break;
            case "Date":
            case "LocalDate":
                vo.setFieldType("string");
                vo.setMockValue("@date");
                break;
            case "LocalDateTime":
            case "ZonedDateTime":
            case "Instant":
                vo.setFieldType("string");
                vo.setMockValue("@datetime");
                break;
            case "List":
            case "Set":
            case "Collection":
                vo.setFieldType("array");
                vo.setMockValue("[]");
                break;
            default:
                vo.setFieldType("object");
                vo.setMockValue("{}");
                break;
        }

        return vo;
    }

    /**
     * 从全限定类名中提取简短类名
     *
     * @param fullTypeName 全限定类名
     * @return 简短类名
     */
    private String extractShortTypeName(String fullTypeName) {
        if (fullTypeName == null || !fullTypeName.contains(".")) {
            return fullTypeName;
        }

        // 处理泛型类型
        if (fullTypeName.contains("<")) {
            fullTypeName = fullTypeName.substring(0, fullTypeName.indexOf("<"));
        }

        return fullTypeName.substring(fullTypeName.lastIndexOf(".") + 1);
    }

    /**
     * 字段值对象，用于生成ApiFox数据模型
     */
    @Data
    private static class FieldVO {
        private String fieldName;
        private String fieldType;
        private String fieldDesc;
        private String mockValue;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("\"").append(fieldName).append("\": {");
            sb.append("\"type\": \"").append(fieldType).append("\",");

            if (StringUtils.hasText(fieldDesc)) {
                sb.append("\"description\": \"").append(fieldDesc).append("\",");
            }

            if (StringUtils.hasText(mockValue)) {
                sb.append("\"mock\": {\"mock\": \"").append(mockValue).append("\"}");
            }

            sb.append("}");
            return sb.toString();
        }

        public String fieldWithDoubleQuote() {
            return "\"" + fieldName + "\"";
        }
    }
}