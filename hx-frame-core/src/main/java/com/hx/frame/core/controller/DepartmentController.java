package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.PageResponse;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.DepartmentQueryCondition;
import com.hx.frame.core.dto.DtoDepartment;
import com.hx.frame.core.service.DepartmentService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 部门控制器
 * 提供部门管理相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
@RestController
@RequestMapping("/api/departments")
public class DepartmentController extends BaseController<DtoDepartment, DepartmentService> {

    /**
     * 创建部门
     * 创建新的部门记录
     *
     * @param department 部门数据对象
     * @return 创建成功的部门对象
     */
    @PostMapping
    public Result<DtoDepartment> create(@RequestBody DtoDepartment department) {
        // 调用服务层保存部门数据
        return Result.success(service.save(department));
    }

    /**
     * 更新部门
     * 更新现有的部门记录
     *
     * @param department 部门数据对象（包含ID）
     * @return 更新后的部门对象
     */
    @PutMapping
    public Result<DtoDepartment> update(@RequestBody DtoDepartment department) {
        // 调用服务层更新部门数据
        return Result.success(service.update(department));
    }

    /**
     * 根据ID获取部门
     * 获取指定ID的部门详细信息
     *
     * @param id 部门ID
     * @return 部门对象
     */
    @GetMapping("/{id}")
    public Result<DtoDepartment> getById(@PathVariable("id") String id) {
        // 调用服务层获取部门数据
        return Result.success(service.findOne(id));
    }

    /**
     * 获取部门树形结构
     * 获取部门的树形结构数据
     *
     * @return 部门树形结构
     */
    @GetMapping("/tree")
    public Result<List<DtoDepartment>> tree() {
        // 调用服务层获取部门树形结构
        return Result.success(service.getDepartmentTree());
    }

    /**
     * 删除单个部门
     * 根据ID删除指定的部门
     *
     * @param id 部门ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(@PathVariable("id") String id) {
        // 调用服务层执行逻辑删除
        service.logicDelete(Collections.singletonList(id));
        return Result.success();
    }

    /**
     * 部门查询接口
     * 支持按部门名称模糊查询和状态精确查询
     *
     * @param condition 查询条件
     * @return 部门分页结果
     */
    @PostMapping("/list")
    public Result<PageResponse<DtoDepartment>> query(@RequestBody DepartmentQueryCondition condition) {
        // 调用服务层执行查询
        Page<DtoDepartment> page = service.findByCondition(condition);

        // 将Page对象转换为PageResponse对象
        PageResponse<DtoDepartment> response = PageResponse.of(page);

        return Result.success(response);
    }

}