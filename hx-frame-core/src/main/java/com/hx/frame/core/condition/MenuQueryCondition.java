package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoMenu;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 菜单查询条件类
 * 用于构建菜单查询的条件参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MenuQueryCondition extends BaseQueryCondition<DtoMenu> {

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 构建查询条件
     * 将查询参数转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 添加菜单名称模糊查询条件
        if (StringUtils.hasText(menuName)) {
            like("menuName", menuName);
        }

        // 添加菜单状态精确查询条件
        if (status != null) {
            eq("status", status);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}
