package com.hx.frame.core.dto;

import com.hx.frame.core.entity.Menu;
import com.hx.frame.util.tree.TreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单数据传输对象
 * 用于与前端交互的菜单数据对象，继承自Menu实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_menu")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoMenu extends Menu implements TreeNode<DtoMenu> {

    /**
     * 父菜单名称
     */
    @Transient
    private String parentName;

    /**
     * 子菜单列表
     */
    @Transient
    private List<DtoMenu> children = new ArrayList<>();

    /**
     * 实现TreeNode接口的getName方法
     *
     * @return 菜单名称
     */
    @Override
    public String getName() {
        return getMenuName();
    }
}
