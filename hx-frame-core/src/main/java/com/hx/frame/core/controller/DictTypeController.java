package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.DictTypeQueryCondition;
import com.hx.frame.core.dto.DtoDictType;
import com.hx.frame.core.service.DictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * 字典类型管理控制器
 * 提供字典类型相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@Slf4j
@RestController
@RequestMapping("/api/dict/type")
public class DictTypeController extends BaseController<DtoDictType, DictTypeService> {

    /**
     * 根据条件查询字典类型列表
     *
     * @param condition 查询条件
     * @return 字典类型分页结果
     */
    @PostMapping("/list")
    public Result<Page<DtoDictType>> list(@RequestBody DictTypeQueryCondition condition) {
        Page<DtoDictType> result = service.findByCondition(condition);
        return Result.success(result);
    }

    /**
     * 根据字典类型编码获取字典类型对象
     *
     * @param dictType 字典类型编码
     * @return 字典类型对象
     */
    @GetMapping("/code/{dictType}")
    public Result<DtoDictType> getDictTypeByType(@PathVariable("dictType") String dictType) {
        DtoDictType dictTypeObj = service.getDictTypeByType(dictType);
        return Result.success(dictTypeObj);
    }

    /**
     * 根据ID获取字典类型
     *
     * @param id 字典类型ID
     * @return 字典类型对象
     */
    @GetMapping("/{id}")
    public Result<DtoDictType> getById(@PathVariable("id") String id) {
        DtoDictType dictType = service.findOne(id);
        return Result.success(dictType);
    }

    /**
     * 新增字典类型
     *
     * @param dictType 字典类型信息
     * @return 操作结果
     */
    @PostMapping
    public Result<DtoDictType> add(@RequestBody DtoDictType dictType) {
        DtoDictType savedDictType = service.save(dictType);
        return Result.success(savedDictType);
    }

    /**
     * 更新字典类型
     *
     * @param dictType 字典类型信息
     * @return 操作结果
     */
    @PutMapping
    public Result<DtoDictType> update(@RequestBody DtoDictType dictType) {
        DtoDictType updatedDictType = service.update(dictType);
        return Result.success(updatedDictType);
    }

    /**
     * 批量删除字典类型
     *
     * @param ids 字典类型ID数组
     * @return 操作结果
     */
    @DeleteMapping
    public Result<Void> batchDelete(@RequestBody Collection<String> ids) {
        service.logicDelete(ids);
        return Result.success();
    }

}
