package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoMenu;

import java.util.List;

/**
 * 菜单仓库接口
 * 提供菜单实体的数据访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
public interface MenuRepository extends IBaseRepository<DtoMenu> {

    /**
     * 根据菜单名称和父菜单ID查询菜单
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @return 菜单对象
     */
    DtoMenu findByMenuNameAndParentId(String menuName, String parentId);

    /**
     * 根据父菜单ID查询子菜单列表
     *
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<DtoMenu> findByParentIdOrderBySortAsc(String parentId);

    /**
     * 查询所有菜单并按排序值升序排列
     *
     * @return 菜单列表
     */
    List<DtoMenu> findAllByOrderBySortAsc();

    /**
     * 根据菜单类型查询菜单列表
     *
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<DtoMenu> findByMenuType(String menuType);

    /**
     * 根据菜单类型和状态查询菜单列表
     *
     * @param menuType 菜单类型
     * @param status   菜单状态
     * @return 菜单列表
     */
    List<DtoMenu> findByMenuTypeAndStatus(String menuType, Integer status);
}
