package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.constant.TreeConstants;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.condition.MenuQueryCondition;
import com.hx.frame.core.dto.DtoMenu;
import com.hx.frame.core.repository.MenuRepository;
import com.hx.frame.core.service.MenuService;
import com.hx.frame.core.service.RoleMenuService;
import com.hx.frame.util.tree.TreeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;

/**
 * 菜单服务实现类
 * 实现菜单相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@Service
public class MenuServiceImpl extends BaseServiceImpl<DtoMenu, MenuRepository> implements MenuService {

    /**
     * 重写保存方法，添加菜单名称唯一性检查
     *
     * @param menu 菜单实体
     * @return 保存后的菜单实体
     */
    @Override
    @Transactional
    public DtoMenu save(DtoMenu menu) {
        // 检查同一父菜单下是否有同名菜单
        if (checkMenuNameExists(menu.getMenuName(), menu.getParentId(), null)) {
            throw new BusinessException("同一菜单下已存在相同名称的菜单");
        }

        // 调用父类的保存方法
        return super.save(menu);
    }

    /**
     * 重写更新方法，添加菜单名称唯一性检查
     *
     * @param menu 菜单实体
     * @return 更新后的菜单实体
     */
    @Override
    @Transactional
    public DtoMenu update(DtoMenu menu) {
        // 检查同一父菜单下是否有同名菜单
        if (checkMenuNameExists(menu.getMenuName(), menu.getParentId(), menu.getId())) {
            throw new BusinessException("同一菜单下已存在相同名称的菜单");
        }

        // 调用父类的更新方法
        return super.update(menu);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoMenu> getMenuTree(MenuQueryCondition condition) {
        List<DtoMenu> menus;

        // 根据条件查询菜单
        if (condition != null) {
            // 构建查询条件
            condition.buildConditions();
            Specification<DtoMenu> spec = condition.toSpecification();

            // 执行查询，按排序字段升序排列
            menus = repository.findAll(spec, Sort.by(Sort.Direction.ASC, "sort"));
        } else {
            // 如果没有条件，获取所有菜单
            menus = repository.findAllByOrderBySortAsc();
        }

        // 否则构建树形结构
        return TreeUtils.buildTree(menus, TreeConstants.ROOT_PARENT_ID);
    }

    /**
     * 检查菜单名称是否已存在
     * 同一父菜单下不能有相同名称的子菜单
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @param id       菜单ID（更新时使用）
     * @return 是否存在
     */
    /**
     * 角色菜单关联服务
     */
    private final RoleMenuService roleMenuService;

    /**
     * 构造函数
     *
     * @param repository 菜单仓库
     * @param roleMenuService 角色菜单关联服务
     */
    @Autowired
    public MenuServiceImpl(MenuRepository repository, RoleMenuService roleMenuService) {
        super.setRepository(repository);
        this.roleMenuService = roleMenuService;
    }

    /**
     * 重写逻辑删除方法，同时删除菜单的角色关联
     *
     * @param ids 菜单ID集合
     */
    @Override
    @Transactional
    public void logicDelete(Collection<String> ids) {
        // 删除菜单的角色关联
        roleMenuService.deleteByMenuIds(ids);

        // 调用父类的逻辑删除方法
        super.logicDelete(ids);
    }

    private boolean checkMenuNameExists(String menuName, String parentId, String id) {
        // 查询同一父菜单下是否有同名菜单
        DtoMenu existingMenu = repository.findByMenuNameAndParentId(menuName, parentId);
        if (existingMenu == null) {
            return false;
        }

        // 如果是更新操作，且找到的是当前菜单，则不算重复
        return !StringUtils.hasText(id) || !existingMenu.getId().equals(id);
    }
}
