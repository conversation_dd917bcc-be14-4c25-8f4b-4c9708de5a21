package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.constant.TreeConstants;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.condition.DepartmentQueryCondition;
import com.hx.frame.core.dto.DtoDepartment;
import com.hx.frame.core.repository.DepartmentRepository;
import com.hx.frame.core.service.DepartmentService;
import com.hx.frame.util.tree.TreeUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 部门服务实现类
 * 实现部门相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
@Service
public class DepartmentServiceImpl extends BaseServiceImpl<DtoDepartment, DepartmentRepository> implements DepartmentService {

    /**
     * 重写保存方法，添加部门名称唯一性检查
     *
     * @param department 部门实体
     * @return 保存后的部门实体
     */
    @Override
    @Transactional
    public DtoDepartment save(DtoDepartment department) {
        // 检查部门名称是否已存在
        if (checkDeptNameExists(department.getDeptName(), null)) {
            throw new BusinessException("部门名称已存在");
        }

        // 调用父类的保存方法
        return super.save(department);
    }

    /**
     * 重写更新方法，添加部门名称唯一性检查
     *
     * @param department 部门实体
     * @return 更新后的部门实体
     */
    @Override
    @Transactional
    public DtoDepartment update(DtoDepartment department) {
        // 检查部门名称是否已存在
        if (checkDeptNameExists(department.getDeptName(), department.getId())) {
            throw new BusinessException("部门名称已存在");
        }

        // 调用父类的更新方法
        return super.update(department);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(readOnly = true)
    public List<DtoDepartment> getDepartmentTree() {
        // 获取所有部门
        List<DtoDepartment> allDepts = repository.findAllByOrderBySortAsc();

        // 使用TreeUtils构建部门树
        return TreeUtils.buildTree(allDepts, TreeConstants.ROOT_PARENT_ID);
    }



    /**
     * 检查部门名称是否已存在
     *
     * @param deptName 部门名称
     * @param id       部门ID（更新时使用）
     * @return 是否存在
     */
    private boolean checkDeptNameExists(String deptName, String id) {
        DtoDepartment existingDept = repository.findByDeptName(deptName);
        if (existingDept == null) {
            return false;
        }

        // 如果是更新操作，且找到的是当前部门，则不算重复
        return !StringUtils.hasText(id) || !existingDept.getId().equals(id);
    }
}
