package com.hx.frame.core.entity;

import com.hx.frame.commons.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

/**
 * 菜单实体类
 * 存储系统菜单的基本信息，包括菜单名称、路径、组件等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class Menu extends BaseEntity {

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 是否为外链（0-否，1-是）
     */
    private Integer isFrame = 0;

    /**
     * 是否缓存（0-否，1-是）
     */
    private Integer isCache = 0;

    /**
     * 菜单类型（M-目录 C-菜单 F-按钮）
     */
    private String menuType;

    /**
     * 菜单状态（0-隐藏，1-显示）
     */
    private Integer visible = 1;

    /**
     * 菜单状态（0-禁用，1-启用）
     */
    private Integer status = 1;

    /**
     * 排序值
     */
    private Integer sort = 0;

    /**
     * 备注
     */
    private String remark;
}
