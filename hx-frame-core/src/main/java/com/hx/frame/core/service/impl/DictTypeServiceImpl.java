package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoDictType;
import com.hx.frame.core.repository.DictTypeRepository;
import com.hx.frame.core.service.DictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * 字典类型服务实现类
 * 实现字典类型相关的业务逻辑
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@Slf4j
@Service
public class DictTypeServiceImpl extends BaseServiceImpl<DtoDictType, DictTypeRepository> implements DictTypeService {

    /**
     * 重写保存方法，添加字典类型唯一性检查
     *
     * @param dictType 字典类型实体
     * @return 保存后的字典类型实体
     */
    @Override
    @Transactional
    public DtoDictType save(DtoDictType dictType) {
        // 检查字典类型是否唯一
        if (checkDictTypeUnique(dictType.getDictType(), null)) {
            throw new BusinessException("新增字典'" + dictType.getDictName() + "'失败，字典类型已存在");
        }

        return super.save(dictType);
    }

    /**
     * 重写更新方法，添加字典类型唯一性检查
     *
     * @param dictType 字典类型实体
     * @return 更新后的字典类型实体
     */
    @Override
    @Transactional
    public DtoDictType update(DtoDictType dictType) {
        // 检查字典类型是否唯一
        if (checkDictTypeUnique(dictType.getDictType(), dictType.getId())) {
            throw new BusinessException("修改字典'" + dictType.getDictName() + "'失败，字典类型已存在");
        }

        return super.update(dictType);
    }

    /**
     * 重写逻辑删除方法，同时删除关联的字典数据
     *
     * @param ids 字典类型ID集合
     */
    @Override
    @Transactional
    public void logicDelete(Collection<String> ids) {
        // 删除字典类型
        super.logicDelete(ids);

        // 删除关联的字典数据

    }

    /**
     * 根据字典类型编码获取字典类型对象
     *
     * @param dictType 字典类型编码
     * @return 字典类型对象
     */
    @Override
    public DtoDictType getDictTypeByType(String dictType) {
        if (!StringUtils.hasText(dictType)) {
            return null;
        }

        return repository.findByDictType(dictType);
    }

    /**
     * 检查字典类型是否唯一
     *
     * @param dictType 字典类型
     * @param id       字典类型ID（更新时使用）
     * @return 结果
     */
    private boolean checkDictTypeUnique(String dictType, String id) {
        DtoDictType dict = repository.findByDictType(dictType);
        if (dict == null) {
            return false;
        }

        // 如果是更新操作，且查询到的是当前记录，则认为类型唯一
        return id == null || !Objects.equals(dict.getId(), id);
    }

}
