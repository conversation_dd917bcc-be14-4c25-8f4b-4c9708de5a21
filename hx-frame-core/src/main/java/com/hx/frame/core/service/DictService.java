package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.dto.DtoDict;

import java.util.List;

/**
 * 字典数据服务接口
 * 定义字典数据相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
public interface DictService extends IBaseService<DtoDict> {

    /**
     * 根据字典类型ID获取字典数据列表
     *
     * @param dictTypeId 字典类型ID
     * @return 字典数据列表
     */
    List<DtoDict> getDictsByDictTypeId(String dictTypeId);

    /**
     * 根据字典类型编码获取字典数据列表
     *
     * @param dictType 字典类型编码
     * @return 字典数据列表
     */
    List<DtoDict> getDictsByDictType(String dictType);

}
