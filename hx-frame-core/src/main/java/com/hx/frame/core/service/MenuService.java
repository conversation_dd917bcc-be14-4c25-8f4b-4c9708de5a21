package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.condition.MenuQueryCondition;
import com.hx.frame.core.dto.DtoMenu;

import java.util.List;

/**
 * 菜单服务接口
 * 定义菜单相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
public interface MenuService extends IBaseService<DtoMenu> {

    /**
     * 根据条件获取菜单树形结构
     *
     * @param condition 查询条件
     * @return 菜单树形结构列表
     */
    List<DtoMenu> getMenuTree(MenuQueryCondition condition);

}
