package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.core.condition.DepartmentQueryCondition;
import com.hx.frame.core.dto.DtoDepartment;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 部门服务接口
 * 定义部门相关的业务操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
public interface DepartmentService extends IBaseService<DtoDepartment> {

    /**
     * 获取部门树形结构
     *
     * @return 部门树形结构列表
     */
    List<DtoDepartment> getDepartmentTree();


}
