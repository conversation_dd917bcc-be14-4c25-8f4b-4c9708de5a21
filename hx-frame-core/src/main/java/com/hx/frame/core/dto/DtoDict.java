package com.hx.frame.core.dto;

import com.hx.frame.core.entity.Dict;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 字典数据传输对象
 * 用于与前端交互的字典数据对象，继承自Dict实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_dict")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoDict extends Dict {

    /**
     * 字典类型名称
     */
    @Transient
    private String dictTypeName;

    /**
     * 字典类型
     */
    @Transient
    private String dictType;
}
