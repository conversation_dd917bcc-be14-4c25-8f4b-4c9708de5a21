package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 用户查询条件类
 * 用于封装用户查询的各种条件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryCondition extends BaseQueryCondition<DtoUser> {

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 构建查询条件
     * 将属性转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        if (StringUtils.hasText(realName)) {
            like("realName", realName);
        }
        if (StringUtils.hasText(phone)) {
            eq("phone", phone);
        }
        if (status != null) {
            eq("status", status);
        }

        // 部门信息
        if (StringUtils.hasText(departmentId)) {
            eq("departmentId", departmentId);
        }
    }
}
