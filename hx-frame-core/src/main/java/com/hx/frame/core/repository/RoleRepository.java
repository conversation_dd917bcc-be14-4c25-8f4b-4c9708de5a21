package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoRole;

import java.util.List;

/**
 * 角色仓库接口
 * 提供角色实体的数据访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-15
 */
public interface RoleRepository extends IBaseRepository<DtoRole> {

    /**
     * 根据角色名称查询角色
     *
     * @param roleName 角色名称
     * @return 角色对象
     */
    DtoRole findByRoleName(String roleName);

    /**
     * 根据角色权限字符串查询角色
     *
     * @param roleKey 角色权限字符串
     * @return 角色对象
     */
    DtoRole findByRoleKey(String roleKey);

    /**
     * 查询所有角色并按排序值升序排列
     *
     * @return 角色列表
     */
    List<DtoRole> findAllByOrderBySortAsc();

    /**
     * 根据状态查询角色列表
     *
     * @param status 角色状态
     * @return 角色列表
     */
    List<DtoRole> findByStatus(Integer status);
}
