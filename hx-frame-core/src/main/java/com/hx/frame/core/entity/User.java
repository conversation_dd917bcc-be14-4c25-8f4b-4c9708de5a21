package com.hx.frame.core.entity;

import com.hx.frame.commons.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

/**
 * 用户实体类
 * 存储系统用户的基本信息，包括用户名、密码、邮箱、手机号等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class User extends BaseEntity {

    /**
     * 用户名，用于登录
     */
    private String username;

    /**
     * 密码，存储加密后的密码
     */
    private String password;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status = 1;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 岗位ID
     */
    private String postId;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 排序值
     */
    private Integer sort = 0;

    /**
     * 备注
     */
    private String remark;
}