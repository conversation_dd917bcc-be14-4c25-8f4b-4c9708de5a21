package com.hx.frame.core.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hx.frame.commons.response.Result;
import com.hx.frame.commons.response.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证入口点
 * 处理未认证用户访问受保护资源的情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Slf4j
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    public JwtAuthenticationEntryPoint(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {
        log.error("未认证的请求: {}", request.getRequestURI(), authException);

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        // 从请求属性中获取自定义异常消息（如果有）
        String errorMessage = (String) request.getAttribute("authErrorMessage");
        if (errorMessage == null) {
            errorMessage = "未提供有效的认证凭证";
        }

        Result<?> result = Result.failed(ResultCode.UNAUTHORIZED, errorMessage);
        objectMapper.writeValue(response.getOutputStream(), result);
    }
}