package com.hx.frame.core.dto;

import com.hx.frame.core.entity.Department;
import com.hx.frame.util.tree.TreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门数据传输对象
 * 用于与前端交互的部门数据对象，继承自Department实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-25
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_dept")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoDepartment extends Department implements TreeNode<DtoDepartment> {

    /**
     * 父部门名称
     */
    @Transient
    private String parentName;

    /**
     * 子部门列表
     */
    @Transient
    private List<DtoDepartment> children = new ArrayList<>();

    /**
     * 实现TreeNode接口的getName方法
     *
     * @return 部门名称
     */
    @Override
    public String getName() {
        return getDeptName();
    }
}
