package com.hx.frame.core.security;

import com.hx.frame.commons.constant.CommonConstants;
import com.hx.frame.commons.enums.EnumUserStatus;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoUser;
import com.hx.frame.core.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * 自定义用户详情服务实现类
 * 实现Spring Security的UserDetailsService接口，用于加载用户特定数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-26
 */
@Slf4j
@Service
public class CustomUserDetailsService implements UserDetailsService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    public CustomUserDetailsService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * 根据用户名加载用户信息
     * 从数据库中查询用户，如果用户不存在或查询出错则抛出相应异常
     *
     * @param username 用户名
     * @return UserDetails对象，包含用户名、加密后的密码和权限信息
     * @throws UsernameNotFoundException 当用户名不存在时抛出此异常
     * @throws BusinessException         当数据库操作出现异常时抛出此异常
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {
            log.debug("开始加载用户信息: {}", username);
            DtoUser user = userRepository.findByUsername(username);

            if (user == null) {
                log.warn("用户不存在: {}", username);
                throw new UsernameNotFoundException("用户不存在: " + username);
            }

            // 检查用户状态
            if (!EnumUserStatus.isEnabled(user.getStatus())) {
                log.warn("用户已被禁用: {}", username);
                throw new BusinessException("用户账号已被禁用");
            }

            log.debug("成功加载用户: {}", username);
            return new User(
                    user.getUsername(),
                    user.getPassword(),
                    Collections.singletonList(new SimpleGrantedAuthority(CommonConstants.Security.ROLE_ADMIN))
            );

        } catch (UsernameNotFoundException | BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("加载用户信息时发生错误: {}", e.getMessage(), e);
            throw new BusinessException("系统错误：加载用户信息失败");
        }
    }
}