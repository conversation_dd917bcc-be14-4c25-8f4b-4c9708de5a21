-- Create user table
CREATE TABLE IF NOT EXISTS hx_frame_user
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    username VARCHAR
(
    50
) NOT NULL COMMENT '用户名',
    password VARCHAR
(
    100
) NOT NULL COMMENT '密码',
    real_name VARCHAR
(
    50
) NOT NULL COMMENT '真实姓名',
    email VARCHAR
(
    100
) COMMENT '邮箱',
    phone VARCHAR
(
    20
) COMMENT '手机号',
    status INT NOT NULL DEFAULT 1 COMMENT '用户状态（0-禁用，1-启用）',
    department_id VARCHAR
(
    50
) COMMENT '部门ID',
    post_id VARCHAR
(
    50
) COMMENT '岗位ID',
    avatar VARCHAR
(
    100
) COMMENT '头像地址',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_username
(
    username
) COMMENT '用户名唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- Create role table
CREATE TABLE IF NOT EXISTS hx_frame_role
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    role_name VARCHAR
(
    50
) NOT NULL COMMENT '角色名称',
    role_key VARCHAR
(
    50
) NOT NULL COMMENT '角色权限字符串',
    status INT NOT NULL DEFAULT 1 COMMENT '角色状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_role_name
(
    role_name
) COMMENT '角色名称唯一索引',
    UNIQUE KEY uk_role_key
(
    role_key
) COMMENT '角色权限字符串唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- Create menu table
CREATE TABLE IF NOT EXISTS hx_frame_menu
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    menu_name VARCHAR
(
    50
) NOT NULL COMMENT '菜单名称',
    parent_id VARCHAR
(
    50
) NOT NULL COMMENT '父菜单ID',
    path VARCHAR
(
    200
) COMMENT '路由地址',
    component VARCHAR
(
    200
) COMMENT '组件路径',
    is_frame INT NOT NULL DEFAULT 0 COMMENT '是否为外链（0-否，1-是）',
    is_cache INT NOT NULL DEFAULT 0 COMMENT '是否缓存（0-否，1-是）',
    menu_type CHAR
(
    1
) NOT NULL COMMENT '菜单类型（M-目录 C-菜单 F-按钮）',
    visible INT NOT NULL DEFAULT 1 COMMENT '菜单状态（0-隐藏，1-显示）',
    status INT NOT NULL DEFAULT 1 COMMENT '菜单状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    KEY idx_parent_id
(
    parent_id
) COMMENT '父菜单ID索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

-- Create department table
CREATE TABLE IF NOT EXISTS hx_frame_dept
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    dept_name VARCHAR
(
    50
) NOT NULL COMMENT '部门名称',
    parent_id VARCHAR
(
    50
) NOT NULL COMMENT '父部门ID',
    status INT NOT NULL DEFAULT 1 COMMENT '部门状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    KEY idx_parent_id
(
    parent_id
) COMMENT '父部门ID索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- Create dictionary type table
CREATE TABLE IF NOT EXISTS hx_frame_dict_type
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    dict_name VARCHAR
(
    50
) NOT NULL COMMENT '字典名称',
    dict_type VARCHAR
(
    50
) NOT NULL COMMENT '字典类型',
    status INT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_dict_type
(
    dict_type
) COMMENT '字典类型唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

-- Create dictionary table
CREATE TABLE IF NOT EXISTS hx_frame_dict
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    dict_type_id VARCHAR
(
    50
) NOT NULL COMMENT '字典类型ID',
    dict_label VARCHAR
(
    50
) NOT NULL COMMENT '字典标签',
    dict_value VARCHAR
(
    50
) NOT NULL COMMENT '字典键值',
    status INT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    KEY idx_dict_type_id
(
    dict_type_id
) COMMENT '字典类型ID索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

-- Create config table
CREATE TABLE IF NOT EXISTS hx_frame_config
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    config_name VARCHAR
(
    50
) NOT NULL COMMENT '参数名称',
    config_key VARCHAR
(
    50
) NOT NULL COMMENT '参数键名',
    config_value VARCHAR
(
    100
) NOT NULL COMMENT '参数键值',
    config_type INT NOT NULL DEFAULT 1 COMMENT '系统内置（0-否，1-是）',
    status INT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_config_key
(
    config_key
) COMMENT '参数键名唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- Create notice table
CREATE TABLE IF NOT EXISTS hx_frame_notice
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    notice_title VARCHAR
(
    50
) NOT NULL COMMENT '公告标题',
    notice_type INT NOT NULL DEFAULT 1 COMMENT '公告类型（1-通知，2-公告）',
    notice_content TEXT NOT NULL COMMENT '公告内容',
    status INT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知公告表';

-- Create login log table
CREATE TABLE IF NOT EXISTS hx_frame_logininfo
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    user_name VARCHAR
(
    50
) NOT NULL COMMENT '用户名',
    ipaddr VARCHAR
(
    50
) NOT NULL COMMENT '登录IP地址',
    login_location VARCHAR
(
    255
) NOT NULL COMMENT '登录地点',
    browser VARCHAR
(
    50
) NOT NULL COMMENT '浏览器类型',
    os VARCHAR
(
    50
) NOT NULL COMMENT '操作系统',
    status INT NOT NULL DEFAULT 1 COMMENT '登录状态（0-失败，1-成功）',
    msg VARCHAR
(
    255
) NOT NULL COMMENT '提示消息',
    login_time DATETIME NOT NULL COMMENT '登录时间',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统访问记录表';

-- Create file table
CREATE TABLE IF NOT EXISTS hx_frame_file
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    file_name VARCHAR
(
    100
) NOT NULL COMMENT '文件名称',
    file_path VARCHAR
(
    200
) NOT NULL COMMENT '文件路径',
    file_type VARCHAR
(
    50
) NOT NULL COMMENT '文件类型',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件信息表';

-- Create job table
CREATE TABLE IF NOT EXISTS hx_frame_job
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    job_name VARCHAR
(
    50
) NOT NULL COMMENT '任务名称',
    job_group VARCHAR
(
    50
) NOT NULL COMMENT '任务组名',
    invoke_target VARCHAR
(
    200
) NOT NULL COMMENT '调用目标字符串',
    cron_expression VARCHAR
(
    200
) NOT NULL COMMENT 'cron执行表达式',
    misfire_policy VARCHAR
(
    20
) NOT NULL COMMENT '计划执行错误策略（1-立即执行，2-执行一次，3-放弃执行）',
    concurrent INT NOT NULL DEFAULT 1 COMMENT '是否并发执行（0-禁止，1-允许）',
    status INT NOT NULL DEFAULT 1 COMMENT '状态（0-暂停，1-正常）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务调度表';

-- Create post table
CREATE TABLE IF NOT EXISTS hx_frame_post
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    post_name VARCHAR
(
    50
) NOT NULL COMMENT '岗位名称',
    post_code VARCHAR
(
    50
) NOT NULL COMMENT '岗位编码',
    status INT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    sort INT DEFAULT 0 COMMENT '排序',
    remark VARCHAR
(
    255
) COMMENT '备注',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR
(
    50
) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_post_code
(
    post_code
) COMMENT '岗位编码唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位信息表';

-- Create user-role relation table
CREATE TABLE IF NOT EXISTS hx_frame_user_role
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    user_id VARCHAR
(
    50
) NOT NULL COMMENT '用户ID',
    role_id VARCHAR
(
    50
) NOT NULL COMMENT '角色ID',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_user_role
(
    user_id,
    role_id
) COMMENT '用户角色关联唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- Create role-menu relation table
CREATE TABLE IF NOT EXISTS hx_frame_role_menu
(
    id
    VARCHAR
(
    50
) COMMENT '主键ID',
    role_id VARCHAR
(
    50
) NOT NULL COMMENT '角色ID',
    menu_id VARCHAR
(
    50
) NOT NULL COMMENT '菜单ID',
    created_by VARCHAR
(
    50
) NOT NULL COMMENT '创建人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted BIT
(
    1
) NOT NULL DEFAULT 0 COMMENT '删除标志（0-未删除，1-已删除）',
    PRIMARY KEY
(
    id
),
    UNIQUE KEY uk_role_menu
(
    role_id,
    menu_id
) COMMENT '角色菜单关联唯一索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';
