-- 插入超级管理员账号
INSERT INTO hx_frame_user (id,
                           username,
                           password,
                           real_name,
                           email,
                           phone,
                           status,
                           deleted,
                           version,
                           created_by,
                           created_time,
                           updated_by,
                           updated_time)
VALUES ('1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed', -- id: UUID
        'admin', -- username: 管理员账号
        '$2a$10$z6bE7.3ZlsrkCTDEaKB6IeUpSWku32Z92Z6zBHHFoUkm4naq91RH2', -- password: admin (BCrypt加密后的密码)
        '超级管理员', -- real_name: 真实姓名
        '<EMAIL>', -- email: 邮箱
        '18934586068', -- phone: 手机号
        1, -- status: 1-启用
        0, -- deleted: 0-未删除
        1, -- version: 版本号
        'system', -- created_by: 创建人
        CURRENT_TIMESTAMP, -- created_time: 创建时间
        'system', -- updated_by: 更新人
        CURRENT_TIMESTAMP -- updated_time: 更新时间
       );
-- 插入根级部门
insert into hx_frame_dept (id, dept_name, parent_id, status, sort, remark, deleted, version, created_by, created_time,
                           updated_by, updated_time)
values (uuid(), '根级部门', '0', 1, 0, null, 0, 1, 'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP);

-- 插入管理员角色
INSERT INTO hx_frame_role (id,
                           role_name,
                           role_key,
                           status,
                           deleted,
                           version,
                           created_by,
                           created_time,
                           updated_by,
                           updated_time)
VALUES ('2c9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed', -- id: UUID
        '超级管理员', -- role_name: 角色名称
        'ROLE_ADMIN', -- role_key: 角色标识
        1, -- status: 1-启用
        0, -- deleted: 0-未删除
        1, -- version: 版本号
        'system', -- created_by: 创建人
        CURRENT_TIMESTAMP, -- created_time: 创建时间
        'system', -- updated_by: 更新人
        CURRENT_TIMESTAMP -- updated_time: 更新时间
       );

-- 关联用户和角色
INSERT INTO hx_frame_user_role (id,
                                user_id,
                                role_id,
                                created_by,
                                created_time,
                                deleted)
VALUES ('3c9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed', -- id: UUID
        '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed', -- user_id: 用户ID
        '2c9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed', -- role_id: 角色ID
        'system', -- created_by: 创建人
        CURRENT_TIMESTAMP, -- created_time: 创建时间
        0 -- deleted: 0-未删除
       );