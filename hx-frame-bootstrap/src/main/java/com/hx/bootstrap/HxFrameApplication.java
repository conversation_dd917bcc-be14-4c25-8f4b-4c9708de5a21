package com.hx.bootstrap;

import com.hx.frame.commons.base.repository.BaseRepository;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * HX Framework启动类
 * 系统的主入口类，负责初始化Spring Boot应用并配置相关组件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-04-18
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.hx")
@EntityScan(basePackages = "com.hx")
@EnableJpaRepositories(basePackages = "com.hx", repositoryBaseClass = BaseRepository.class)
@EnableTransactionManagement
@EnableAsync
public class HxFrameApplication {

    /**
     * 应用程序入口方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(HxFrameApplication.class, args);
    }
}