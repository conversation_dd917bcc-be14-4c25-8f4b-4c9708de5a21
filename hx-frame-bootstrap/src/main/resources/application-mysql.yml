spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:rm-uf6hlg78p8f36m81l7o.mysql.rds.aliyuncs.com}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:hx_frame}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&useSSL=false
    username: ${MYSQL_USERNAME:wms01}
    password: ${MYSQL_PASSWORD:Sunpeng99}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 300000
      connection-timeout: 20000
      connection-test-query: SELECT 1
      max-lifetime: 1200000 