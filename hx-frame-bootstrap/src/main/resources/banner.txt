${AnsiColor.BRIGHT_BLUE}  _    _  __   __      ${AnsiColor.BRIGHT_RED}  ______   _____       ___      __  __   ______
${AnsiColor.BRIGHT_BLUE} | |  | | \ \ / /      ${AnsiColor.BRIGHT_RED} |  ____| |  __ \     / _ \    |  \/  | |  ____|
${AnsiColor.BRIGHT_BLUE} | |__| |  \ V /       ${AnsiColor.BRIGHT_RED} | |__    | |__) |   / /_\ \   | \  / | | |__
${AnsiColor.BRIGHT_BLUE} |  __  |   > <        ${AnsiColor.BRIGHT_RED} |  __|   |  _  /   / _____ \  | |\/| | |  __|
${AnsiColor.BRIGHT_BLUE} | |  | |  / . \       ${AnsiColor.BRIGHT_RED} | |      | | \ \  / /     \ \ | |  | | | |____
${AnsiColor.BRIGHT_BLUE} |_|  |_| /_/ \_\      ${AnsiColor.BRIGHT_RED} |_|      |_|  \_\/_/       \_\|_|  |_| |______|

${AnsiColor.BRIGHT_GREEN}:: HX-Frame :: ${AnsiColor.DEFAULT}(v1.0.0-SNAPSHOT)
${AnsiColor.BRIGHT_GREEN}:: Author :: ${AnsiColor.DEFAULT}(baoyc)
${AnsiColor.BRIGHT_GREEN}:: Email :: ${AnsiColor.DEFAULT}(<EMAIL>)
${AnsiColor.BRIGHT_GREEN}:: Spring Boot :: ${AnsiColor.DEFAULT}${spring-boot.version}
${AnsiColor.DEFAULT}
