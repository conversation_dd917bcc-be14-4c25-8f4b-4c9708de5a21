HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Logs ###
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

### Environment Variables ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### Docker ###
docker-compose.override.yml

### Other ###
.DS_Store
Thumbs.db 