package com.hx.frame.util.tree;

import java.util.List;

/**
 * 树节点接口
 * 定义树形结构节点的基本操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-26
 */
public interface TreeNode<T extends TreeNode<T>> {

    /**
     * 获取节点ID
     *
     * @return 节点ID
     */
    String getId();

    /**
     * 获取父节点ID
     *
     * @return 父节点ID
     */
    String getParentId();

    /**
     * 获取子节点列表
     *
     * @return 子节点列表
     */
    List<T> getChildren();

    /**
     * 设置子节点列表
     *
     * @param children 子节点列表
     */
    void setChildren(List<T> children);

    /**
     * 设置父节点名称
     *
     * @param parentName 父节点名称
     */
    void setParentName(String parentName);

    /**
     * 获取节点名称
     *
     * @return 节点名称
     */
    String getName();
}