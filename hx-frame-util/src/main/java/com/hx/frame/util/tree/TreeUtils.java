package com.hx.frame.util.tree;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 树形结构工具类
 * 提供构建树形结构的通用方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-26
 */
public class TreeUtils {

    /**
     * 构建树形结构
     *
     * @param nodes    节点列表
     * @param parentId 父节点ID
     * @param <T>      节点类型
     * @return 树形结构
     */
    public static <T extends TreeNode<T>> List<T> buildTree(List<T> nodes, String parentId) {
        if (nodes == null || nodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建节点ID到节点的映射，用于快速查找父节点
        Map<String, T> nodeMap = nodes.stream()
                .collect(Collectors.toMap(TreeNode::getId, Function.identity(), (a, b) -> a));

        return nodes.stream()
                .filter(node -> Objects.equals(node.getParentId(), parentId))
                .peek(node -> {
                    // 设置父节点名称
                    if (!Objects.equals(parentId, "0") && nodeMap.containsKey(parentId)) {
                        node.setParentName(nodeMap.get(parentId).getName());
                    }

                    // 递归设置子节点
                    List<T> children = buildTree(nodes, node.getId());
                    node.setChildren(children);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取节点及其所有子节点
     *
     * @param nodes 所有节点列表
     * @param id    节点ID
     * @param <T>   节点类型
     * @return 包含子节点的节点
     */
    public static <T extends TreeNode<T>> T getNodeWithChildren(List<T> nodes, String id) {
        if (nodes == null || nodes.isEmpty() || id == null) {
            return null;
        }

        // 查找指定节点
        T node = nodes.stream()
                .filter(n -> Objects.equals(n.getId(), id))
                .findFirst()
                .orElse(null);

        if (node == null) {
            return null;
        }

        // 构建子节点树
        List<T> children = buildTree(nodes, id);
        node.setChildren(children);

        return node;
    }
}