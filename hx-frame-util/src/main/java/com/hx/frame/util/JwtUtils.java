package com.hx.frame.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT工具类
 * 提供JWT令牌的生成、解析和验证功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-18
 */
@Slf4j
public class JwtUtils {
    /**
     * 令牌类型常量
     */
    public static final String TOKEN_TYPE_BEARER = "Bearer";

    /**
     * 每分钟的毫秒数
     */
    private static final long MILLISECONDS_PER_MINUTE = 60 * 1000;

    /**
     * JWT密钥
     */
    private static SecretKey key;

    /**
     * 访问令牌有效期（毫秒）
     */
    private static long accessTokenValidity;

    /**
     * 刷新令牌有效期（毫秒）
     */
    private static long refreshTokenValidity;

    /**
     * 初始化JWT配置
     *
     * @param secret                        密钥字符串
     * @param accessTokenValidityInMinutes  访问令牌有效期（分钟）
     * @param refreshTokenValidityInMinutes 刷新令牌有效期（分钟）
     */
    public static void init(String secret, long accessTokenValidityInMinutes, long refreshTokenValidityInMinutes) {
        key = Keys.hmacShaKeyFor(secret.getBytes());
        accessTokenValidity = accessTokenValidityInMinutes * MILLISECONDS_PER_MINUTE;
        refreshTokenValidity = refreshTokenValidityInMinutes * MILLISECONDS_PER_MINUTE;
    }

    /**
     * 生成访问令牌
     *
     * @param userDetails 用户详情对象
     * @return 生成的JWT访问令牌字符串
     */
    public static String generateAccessToken(UserDetails userDetails) {
        return generateToken(new HashMap<>(), userDetails.getUsername(), accessTokenValidity);
    }

    /**
     * 生成刷新令牌
     *
     * @param userDetails 用户详情对象
     * @return 生成的JWT刷新令牌字符串
     */
    public static String generateRefreshToken(UserDetails userDetails) {
        return generateToken(new HashMap<>(), userDetails.getUsername(), refreshTokenValidity);
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token JWT令牌字符串
     * @return 用户名，如果令牌无效或过期则返回null
     */
    public static String extractUsername(String token) {
        try {
            return extractClaim(token, Claims::getSubject);
        } catch (ExpiredJwtException e) {
            log.warn("令牌已过期: {}", e.getMessage());
            return null;
        } catch (JwtException e) {
            log.error("令牌解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证令牌是否有效
     *
     * @param token       JWT令牌
     * @param userDetails 用户详情
     * @return 如果令牌有效则返回true
     */
    public static boolean validateToken(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username != null && username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    /**
     * 生成JWT令牌
     *
     * @param claims   自定义声明
     * @param subject  主题（通常是用户名）
     * @param validity 有效期（毫秒）
     * @return 生成的JWT令牌字符串
     */
    private static String generateToken(Map<String, Object> claims, String subject, long validity) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + validity);

        return Jwts.builder()
                .claims(claims)
                .subject(subject)
                .issuedAt(now)
                .expiration(expiryDate)
                .signWith(key)
                .compact();
    }

    /**
     * 从令牌中提取所有声明
     *
     * @param token JWT令牌字符串
     * @return 声明对象
     * @throws JwtException 如果令牌无效或解析失败
     */
    private static Claims extractAllClaims(String token) throws JwtException {
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /**
     * 从令牌中提取特定声明
     *
     * @param token          JWT令牌字符串
     * @param claimsResolver 声明解析函数
     * @param <T>            返回类型
     * @return 解析后的声明值
     * @throws JwtException 如果令牌无效或解析失败
     */
    private static <T> T extractClaim(String token, Function<Claims, T> claimsResolver) throws JwtException {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 检查令牌是否已过期
     *
     * @param token JWT令牌
     * @return 如果令牌已过期则返回true
     */
    private static boolean isTokenExpired(String token) {
        try {
            final Date expiration = extractExpiration(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 从令牌中提取过期时间
     *
     * @param token JWT令牌
     * @return 过期时间
     */
    public static Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }
}