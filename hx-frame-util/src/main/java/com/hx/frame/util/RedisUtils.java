package com.hx.frame.util;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis操作工具类
 * 基于Redisson实现，提供常用的Redis操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
@Slf4j
@Component
public class RedisUtils {

    private final RedissonClient redissonClient;

    /**
     * 构造函数
     *
     * @param redissonClient Redisson客户端
     */
    public RedisUtils(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    // ========== String操作 ==========

    /**
     * 设置字符串值
     *
     * @param key   键
     * @param value 值
     */
    public <T> void set(String key, T value) {
        try {
            RBucket<T> bucket = redissonClient.getBucket(key);
            bucket.set(value);
        } catch (Exception e) {
            log.error("Redis set error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 设置字符串值并设置过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public <T> void set(String key, T value, long timeout, TimeUnit unit) {
        try {
            RBucket<T> bucket = redissonClient.getBucket(key);
            bucket.set(value, timeout, unit);
        } catch (Exception e) {
            log.error("Redis set with timeout error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取字符串值
     *
     * @param key 键
     * @return 值
     */
    public <T> T get(String key) {
        try {
            RBucket<T> bucket = redissonClient.getBucket(key);
            return bucket.get();
        } catch (Exception e) {
            log.error("Redis get error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 是否成功
     */
    public boolean delete(String key) {
        try {
            return redissonClient.getBucket(key).delete();
        } catch (Exception e) {
            log.error("Redis delete error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 批量删除键
     *
     * @param keys 键集合
     * @return 删除的键数量
     */
    public long deleteMulti(Collection<String> keys) {
        try {
            return redissonClient.getKeys().delete(keys.toArray(new String[0]));
        } catch (Exception e) {
            log.error("Redis batch delete error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 设置过期时间
     *
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return 是否成功
     */
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            return redissonClient.getBucket(key).expire(timeout, unit);
        } catch (Exception e) {
            log.error("Redis expire error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 判断键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public boolean hasKey(String key) {
        try {
            return redissonClient.getBucket(key).isExists();
        } catch (Exception e) {
            log.error("Redis hasKey error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    // ========== Hash操作 ==========

    /**
     * 设置Hash字段值
     *
     * @param key     键
     * @param hashKey Hash字段
     * @param value   值
     */
    public <T> void hSet(String key, String hashKey, T value) {
        try {
            RMap<String, T> map = redissonClient.getMap(key);
            map.put(hashKey, value);
        } catch (Exception e) {
            log.error("Redis hSet error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取Hash字段值
     *
     * @param key     键
     * @param hashKey Hash字段
     * @return 值
     */
    public <T> T hGet(String key, String hashKey) {
        try {
            RMap<String, T> map = redissonClient.getMap(key);
            return map.get(hashKey);
        } catch (Exception e) {
            log.error("Redis hGet error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 删除Hash字段
     *
     * @param key      键
     * @param hashKeys Hash字段集合
     * @return 删除的字段数量
     */
    public long hDelete(String key, String... hashKeys) {
        try {
            RMap<String, Object> map = redissonClient.getMap(key);
            return map.fastRemove(hashKeys);
        } catch (Exception e) {
            log.error("Redis hDelete error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 判断Hash字段是否存在
     *
     * @param key     键
     * @param hashKey Hash字段
     * @return 是否存在
     */
    public boolean hHasKey(String key, String hashKey) {
        try {
            RMap<String, Object> map = redissonClient.getMap(key);
            return map.containsKey(hashKey);
        } catch (Exception e) {
            log.error("Redis hHasKey error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取Hash所有字段值
     *
     * @param key 键
     * @return 所有字段值
     */
    public <T> Map<String, T> hGetAll(String key) {
        try {
            RMap<String, T> map = redissonClient.getMap(key);
            return map.readAllMap();
        } catch (Exception e) {
            log.error("Redis hGetAll error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    // ========== List操作 ==========

    /**
     * 从List左侧添加元素
     *
     * @param key   键
     * @param value 值
     * @return 添加后的List长度
     */
    public <T> int lLeftPush(String key, T value) {
        try {
            RList<T> list = redissonClient.getList(key);
            list.add(0, value);
            return list.size();
        } catch (Exception e) {
            log.error("Redis lLeftPush error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 从List右侧添加元素
     *
     * @param key   键
     * @param value 值
     * @return 添加后的List长度
     */
    public <T> int lRightPush(String key, T value) {
        try {
            RList<T> list = redissonClient.getList(key);
            list.add(value);
            return list.size();
        } catch (Exception e) {
            log.error("Redis lRightPush error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取List指定范围的元素
     *
     * @param key   键
     * @param start 开始索引
     * @param end   结束索引
     * @return 元素列表
     */
    public <T> List<T> lRange(String key, int start, int end) {
        try {
            RList<T> list = redissonClient.getList(key);
            return list.range(start, end);
        } catch (Exception e) {
            log.error("Redis lRange error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取List长度
     *
     * @param key 键
     * @return List长度
     */
    public int lSize(String key) {
        try {
            RList<Object> list = redissonClient.getList(key);
            return list.size();
        } catch (Exception e) {
            log.error("Redis lSize error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    // ========== Set操作 ==========

    /**
     * 向Set添加元素
     *
     * @param key    键
     * @param values 值集合
     * @return 添加后的Set长度
     */
    public <T> int sAdd(String key, T... values) {
        try {
            RSet<T> set = redissonClient.getSet(key);
            // 使用兼容Java 8的方式创建Set
            Set<T> valueSet = new java.util.HashSet<>();
            for (T value : values) {
                valueSet.add(value);
            }
            // 添加元素
            set.addAll(valueSet);
            // 返回集合大小
            return set.size();
        } catch (Exception e) {
            log.error("Redis sAdd error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取Set所有元素
     *
     * @param key 键
     * @return 元素集合
     */
    public <T> Set<T> sMembers(String key) {
        try {
            RSet<T> set = redissonClient.getSet(key);
            return set.readAll();
        } catch (Exception e) {
            log.error("Redis sMembers error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 判断Set是否包含元素
     *
     * @param key   键
     * @param value 值
     * @return 是否包含
     */
    public <T> boolean sIsMember(String key, T value) {
        try {
            RSet<T> set = redissonClient.getSet(key);
            return set.contains(value);
        } catch (Exception e) {
            log.error("Redis sIsMember error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取Set长度
     *
     * @param key 键
     * @return Set长度
     */
    public int sSize(String key) {
        try {
            RSet<Object> set = redissonClient.getSet(key);
            return set.size();
        } catch (Exception e) {
            log.error("Redis sSize error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    // ========== ZSet操作 ==========

    /**
     * 向ZSet添加元素
     *
     * @param key   键
     * @param value 值
     * @param score 分数
     * @return 是否成功
     */
    public <T> boolean zAdd(String key, T value, double score) {
        try {
            RScoredSortedSet<T> zset = redissonClient.getScoredSortedSet(key);
            return zset.add(score, value);
        } catch (Exception e) {
            log.error("Redis zAdd error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取ZSet指定范围的元素
     *
     * @param key   键
     * @param start 开始索引
     * @param end   结束索引
     * @return 元素集合
     */
    public <T> List<T> zRange(String key, int start, int end) {
        try {
            RScoredSortedSet<T> zset = redissonClient.getScoredSortedSet(key);
            // 使用兼容的方式获取指定范围的元素
            Collection<T> values = zset.valueRange(start, end);
            // 转换为List返回
            return new java.util.ArrayList<>(values);
        } catch (Exception e) {
            log.error("Redis zRange error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取ZSet元素的分数
     *
     * @param key   键
     * @param value 值
     * @return 分数
     */
    public <T> Double zScore(String key, T value) {
        try {
            RScoredSortedSet<T> zset = redissonClient.getScoredSortedSet(key);
            return zset.getScore(value);
        } catch (Exception e) {
            log.error("Redis zScore error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    /**
     * 获取ZSet长度
     *
     * @param key 键
     * @return ZSet长度
     */
    public int zSize(String key) {
        try {
            RScoredSortedSet<Object> zset = redissonClient.getScoredSortedSet(key);
            return zset.size();
        } catch (Exception e) {
            log.error("Redis zSize error: {}", e.getMessage(), e);
            throw new RuntimeException("Redis操作异常", e);
        }
    }

    // ========== 分布式锁操作 ==========

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey   锁键
     * @param waitTime  等待时间
     * @param leaseTime 持有锁的时间
     * @param unit      时间单位
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit) {
        try {
            return redissonClient.getLock(lockKey).tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Redis tryLock interrupted: {}", e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("Redis tryLock error: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁键
     */
    public void unlock(String lockKey) {
        try {
            redissonClient.getLock(lockKey).unlock();
        } catch (Exception e) {
            log.error("Redis unlock error: {}", e.getMessage(), e);
        }
    }
}
